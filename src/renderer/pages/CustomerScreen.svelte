<script>
  import { onMount } from 'svelte'

  let salesItems = []

  onMount(() => {
    if (window.electronAPI && window.electronAPI.onSalesUpdate) {
      window.electronAPI.onSalesUpdate(items => {
        salesItems = items
      })
    }

    window.addEventListener('message', event => {
      if (event.data.type === 'SALES_UPDATE') {
        salesItems = event.data.items
      }
    })
  })

  function formatPrice(price) {
    return price.toLocaleString('tr-TR', {
      style: 'currency',
      currency: 'TRY',
    })
  }
</script>

<div class="customer-screen-fullwidth">
  <div class="customer-left-panel">
    <div class="customer-sales-list">
      <h2 class="customer-title"><PERSON>r<PERSON>n<PERSON></h2>
      {#if salesItems.length === 0}
        <p class="customer-empty">Hen<PERSON>z ürün eklenmedi</p>
      {:else}
        <div class="customer-items">
          {#each salesItems as item (item.id || item.sequenceNo)}
            <div class="customer-item">
              <div class="customer-item-single-line">
                <span class="customer-item-info">{item.quantity} {item.unit} X {item.name}</span>
                <span class="customer-price">{formatPrice(item.total)}</span>
              </div>
            </div>
          {/each}
        </div>
      {/if}
    </div>
  </div>

  <div class="customer-right-panel">
    <div class="customer-advertisement-area customer-top-ads">
      <!-- Üst reklam alanı -->
    </div>
    <div class="customer-advertisement-area customer-bottom-ads">
      <!-- Alt reklam alanı -->
    </div>
  </div>
</div>

<style>
  /* CustomerScreen için tamamen özgün CSS - diğer sayfaları etkilemez */
  .customer-screen-fullwidth {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    display: flex !important;
    flex-direction: row !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    color: #2c3e50 !important;
    overflow: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    z-index: 9999 !important;
  }

  .customer-left-panel {
    width: 50vw !important;
    height: 100vh !important;
    padding: 1.5rem !important;
    background: #ffffff !important;
    border-right: 2px solid #dee2e6 !important;
    overflow-y: auto !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
    box-sizing: border-box !important;
    flex: none !important;
    min-width: 50vw !important;
    max-width: 50vw !important;
  }

  .customer-right-panel {
    width: 50vw !important;
    height: 100vh !important;
    padding: 1.5rem !important;
    background: #f8f9fa !important;
    overflow-y: auto !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 1rem !important;
    box-sizing: border-box !important;
    flex: none !important;
    min-width: 50vw !important;
    max-width: 50vw !important;
  }

  .customer-sales-list {
    max-width: 100% !important;
    width: 100% !important;
  }

  .customer-advertisement-area {
    flex: 1 !important;
    background: #ffffff !important;
    border: 2px dashed #ced4da !important;
    border-radius: 0.5rem !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #6c757d !important;
    font-size: 0.94rem !important;
    height: calc(50vh - 4rem) !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .customer-top-ads::before {
    content: 'Üst Reklam Alanı' !important;
    opacity: 0.5 !important;
  }

  .customer-bottom-ads::before {
    content: 'Alt Reklam Alanı' !important;
    opacity: 0.5 !important;
  }

  .customer-title {
    font-size: 1.7rem !important;
    margin-bottom: 1.5rem !important;
    text-align: center !important;
    color: #495057 !important;
    font-weight: 600 !important;
  }

  .customer-empty {
    text-align: center !important;
    font-size: 1.02rem !important;
    color: #6c757d !important;
    margin-top: 2rem !important;
  }

  .customer-items {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .customer-item {
    background: #f8f9fa !important;
    padding: 1rem !important;
    border-radius: 0.5rem !important;
    border: 1px solid #e9ecef !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    transition: box-shadow 0.2s ease !important;
    margin: 0 !important;
  }

  .customer-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  }

  .customer-item-single-line {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
  }

  .customer-item-info {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: #343a40 !important;
    flex: 1 !important;
  }

  .customer-price {
    font-size: 1.1rem !important;
    font-weight: bold !important;
    color: #28a745 !important;
    margin-left: 1rem !important;
  }
</style>
