<script>
  console.log('🔥 TestKeyboard component loaded!')
</script>

<!-- Test Component -->
<div
  style="position: fixed; top: 50px; right: 10px; background: green; color: white; padding: 17px; z-index: 10000; font-size: 14px; border: 3px solid blue;"
>
  ✅ TEST KEYBOARD COMPONENT WORKING!
</div>

<!-- Test Button -->
<button
  style="position: fixed; bottom: 20px; right: 20px; width: 51px; height: 51px; border-radius: 50%; background: orange; color: white; border: none; font-size: 20px; cursor: pointer; z-index: 9999;"
  on:click={() => alert('Test button clicked!')}
>
  🎹
</button>
