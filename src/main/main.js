import { app, BrowserWindow } from 'electron'
import path from 'path'
import { fileURLToPath, URL } from 'url'
import authService from './auth.js'
import { APP_CONFIG, PROJECT_CONFIG } from './config.js'
import { getDatabase, initDatabase } from './database.js'
import { setCreateCustomerWindow, setCustomerWindow } from './ipc/app.js'
import { setupIpcHandlers } from './ipc/index.js'
import { initializeRabbitMQ, shutdownRabbitMQ } from './rabbitmq.js'
import { applyDatabaseSchema } from './schema.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

let mainWindow
let customerWindow

function createWindow() {
  console.log('🪟 Creating Electron window...')

  const preloadPath = path.join(__dirname, 'preload.js')
  console.log('🔧 Preload script path:', preloadPath)

  mainWindow = new BrowserWindow({
    width: APP_CONFIG.window.width,
    height: APP_CONFIG.window.height,
    title: PROJECT_CONFIG.name,
    fullscreen: APP_CONFIG.window.fullscreen,
    webPreferences: {
      nodeIntegration: false, // Secure: disable node integration
      contextIsolation: true, // Secure: enable context isolation
      webSecurity: true, // Secure: enable web security
      allowRunningInsecureContent: false, // Secure: disable insecure content
      experimentalFeatures: false, // Secure: disable experimental features
      preload: preloadPath, // Secure preload script for IPC
    },
  })

  // No remote module needed - using secure IPC

  console.log('🪟 Electron window created')

  // Add event listeners for debugging
  mainWindow.webContents.on('did-start-loading', () => {
    console.log('🔄 Window started loading...')
  })

  mainWindow.webContents.on('did-finish-load', () => {
    console.log('✅ Window finished loading')
  })

  mainWindow.webContents.on('did-fail-load', (_, errorCode, errorDescription) => {
    console.error('❌ Window failed to load:', errorCode, errorDescription)
  })

  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Load application based on NODE_ENV
  if (process.env.NODE_ENV === 'development') {
    const devUrl = 'http://localhost:5173'
    console.log('🔗 Loading development URL:', devUrl)
    mainWindow.loadURL(devUrl)

    // Open DevTools automatically in development
    console.log('🛠️ Opening dev tools...')
    mainWindow.webContents.openDevTools()
  } else {
    // Load built application
    const prodPath = path.join(__dirname, '../../dist/index.html')
    console.log('🔗 Loading production file:', prodPath)
    mainWindow.loadFile(prodPath)
  }

  // Security: Prevent new window creation
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    console.log('🚫 Blocked new window creation for:', url)
    return { action: 'deny' }
  })
}

function createCustomerWindow() {
  // Eğer müşteri penceresi zaten açıksa, focus yap
  if (customerWindow) {
    customerWindow.focus()
    return
  }

  console.log('🪟 Creating Customer window...')

  const preloadPath = path.join(__dirname, 'preload.js')

  customerWindow = new BrowserWindow({
    width: 800,
    height: 600,
    title: 'Müşteri Ekranı',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      preload: preloadPath,
    },
  })

  customerWindow.on('closed', () => {
    customerWindow = null
  })

  setCustomerWindow(customerWindow)

  // Load customer screen
  if (process.env.NODE_ENV === 'development') {
    const devUrl = 'http://localhost:5173/#/customer'
    console.log('🔗 Loading customer screen URL:', devUrl)
    customerWindow.loadURL(devUrl)
  } else {
    const prodPath = path.join(__dirname, '../../dist/index.html')
    customerWindow.loadFile(prodPath)
    // Navigate to customer route after load
    customerWindow.webContents.once('did-finish-load', () => {
      customerWindow.webContents.executeJavaScript('window.location.hash = "#/customer"')
    })
  }

  console.log('✅ Customer window created')
}

/**
 * Initialize database schema by applying database schema
 */
function createDatabaseSchema() {
  try {
    console.log('🔧 Creating database schema...')
    const db = getDatabase()
    applyDatabaseSchema(db)
    console.log('✅ Database schema created successfully')
  } catch (error) {
    console.error('❌ Error creating database schema:', error)
    throw error
  }
}

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  console.log(`🚀 Starting ShopigoFurpa v${PROJECT_CONFIG.version}`)

  // Setup secure IPC communication
  setupIpcHandlers()

  // Set customer window creation function for IPC
  setCreateCustomerWindow(createCustomerWindow)

  // Initialize database first - this is CRITICAL
  try {
    console.log('📡 Initializing database...')
    initDatabase() // Remove await since initDatabase is synchronous
    console.log('✅ Database initialized successfully')
  } catch (error) {
    console.error('❌ CRITICAL: Database initialization failed:', error)
    console.error('❌ Application cannot start without database')
    app.quit()
    return
  }

  // Create database schema - this is also CRITICAL
  try {
    console.log('📊 Creating database schema...')
    createDatabaseSchema()
    console.log('✅ Database schema created successfully')
  } catch (error) {
    console.error('❌ CRITICAL: Database schema creation failed:', error)
    console.error('❌ Application cannot start without proper schema')
    app.quit()
    return
  }

  // Initialize RabbitMQ after database
  try {
    await initializeRabbitMQ()
  } catch {
    // RabbitMQ initialization is optional
  }

  // Initialize authentication service - requires database
  try {
    console.log('🔐 Initializing authentication service...')
    await authService.initialize()
    console.log('✅ Authentication service initialized successfully')
  } catch (error) {
    console.error('❌ CRITICAL: Authentication service initialization failed:', error)
    console.error('❌ Application cannot start without authentication')
    app.quit()
    return
  }

  // Create the main window
  createWindow()

  app.on('activate', () => {
    // On macOS, re-create window when dock icon is clicked
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Security: Prevent navigation to external URLs
app.on('web-contents-created', (_, contents) => {
  contents.on('will-navigate', (navigationEvent, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)

    // Allow localhost during development
    if (process.env.NODE_ENV === 'development' && parsedUrl.hostname === 'localhost') {
      return
    }

    // Allow file protocol for production builds
    if (parsedUrl.protocol === 'file:') {
      return
    }

    console.log('🚫 Blocked navigation to:', navigationUrl)
    navigationEvent.preventDefault()
  })
})

// Security: Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  // Allow self-signed certificates for localhost during development
  if (process.env.NODE_ENV === 'development' && url.includes('localhost')) {
    event.preventDefault()
    callback(true)
    return
  }

  // In production, use default behavior
  callback(false)
})

// Handle app termination
app.on('before-quit', async () => {
  console.log('🚪 Application shutting down...')

  // Cleanup RabbitMQ connection
  try {
    await shutdownRabbitMQ()
    console.log('✅ RabbitMQ disconnected successfully')
  } catch (error) {
    console.error('⚠️ Error disconnecting RabbitMQ:', error)
  }
})

// Handle second instance (prevent multiple instances)
app.on('second-instance', () => {
  // Focus the existing window if a second instance is opened
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore()
    }
    mainWindow.focus()
  }
})

// Ensure single instance
if (!app.requestSingleInstanceLock()) {
  console.log('🚫 Another instance is already running')
  app.quit()
}
