/**
 * Secure preload script for IPC communication
 * Exposes only necessary IPC channels via contextBridge
 * Follows Electron security best practices
 */

console.log('🔧 Secure preload script starting...')

const { contextBridge, ipcRenderer } = require('electron')

console.log('🔧 Preload script loaded contextBridge and ipcRenderer')

// Secure API object that will be exposed to renderer process
const electronAPI = {
  // Database operations via IPC
  database: {
    searchInventory: searchTerm => ipcRenderer.invoke('database:searchInventory', searchTerm),
    getAllUsers: () => ipcRenderer.invoke('database:getAllUsers'),
    addUser: userData => ipcRenderer.invoke('database:addUser', userData),
    updateUser: (id, userData) => ipcRenderer.invoke('database:updateUser', id, userData),
    deleteUser: id => ipcRenderer.invoke('database:deleteUser', id),
    saveSaleTransaction: saleData => ipcRenderer.invoke('database:saveSaleTransaction', saleData),
    getTodaysSales: () => ipcRenderer.invoke('database:getTodaysSales'),
    getSalesWithFilters: options => ipcRenderer.invoke('database:getSalesWithFilters', options),
    getSaleDetails: saleUuid => ipcRenderer.invoke('database:getSaleDetails', saleUuid),
    // Sales management functions
    getTodaysInProgressSale: () => ipcRenderer.invoke('database:getTodaysInProgressSale'),
    createInProgressSale: saleData => ipcRenderer.invoke('database:createInProgressSale', saleData),
    addItemToInProgressSale: (saleUuid, item) =>
      ipcRenderer.invoke('database:addItemToInProgressSale', saleUuid, item),
    completeInProgressSale: (saleUuid, payments) =>
      ipcRenderer.invoke('database:completeInProgressSale', saleUuid, payments),
    generateSalesNumber: () => ipcRenderer.invoke('database:generateSalesNumber'),
  },

  // Data import operations via IPC
  importUserData: data => ipcRenderer.invoke('database:importUserData', data),

  // Database sync operations via IPC
  syncInventory: ipNo => ipcRenderer.invoke('syncInventory', ipNo),
  syncInventoryDB: ipNo => ipcRenderer.invoke('syncInventoryDB', ipNo),
  connectPs: (kasaNo, ipNo) => ipcRenderer.invoke('connectPs', kasaNo, ipNo),
  getInventoryRecordes: ipNo => ipcRenderer.invoke('getInventoryRecordes', ipNo),

  // Authentication operations via IPC
  auth: {
    login: credentials => ipcRenderer.invoke('auth:login', credentials),
    logout: () => ipcRenderer.invoke('auth:logout'),
    isUserAuthenticated: () => ipcRenderer.invoke('auth:isUserAuthenticated'),
    getCurrentUser: () => ipcRenderer.invoke('auth:getCurrentUser'),
    getRememberedUsername: () => ipcRenderer.invoke('auth:getRememberedUsername'),
  },

  // Configuration operations via IPC
  config: {
    getProjectConfig: () => ipcRenderer.invoke('config:getProjectConfig'),
    getAppConfig: () => ipcRenderer.invoke('config:getAppConfig'),
  },

  // RabbitMQ operations via IPC
  rabbitmq: {
    init: () => ipcRenderer.invoke('rabbitmq:init'),
    getStatus: () => ipcRenderer.invoke('rabbitmq:getStatus'),
    getConsumers: () => ipcRenderer.invoke('rabbitmq:getConsumers'),
    retryConsumers: () => ipcRenderer.invoke('rabbitmq:retryConsumers'),
    testConsumerSetup: () => ipcRenderer.invoke('rabbitmq:testConsumerSetup'),
    forceInit: () => ipcRenderer.invoke('rabbitmq:forceInit'),
    checkQueues: () => ipcRenderer.invoke('rabbitmq:checkQueues'),
    testEmployeeQueue: () => ipcRenderer.invoke('rabbitmq:testEmployeeQueue'),
    forceEmployeeFullSync: () => ipcRenderer.invoke('rabbitmq:forceEmployeeFullSync'),
    getDetailedChannelStats: () => ipcRenderer.invoke('rabbitmq:getDetailedChannelStats'),
    publishMessage: (queueName, message) =>
      ipcRenderer.invoke('rabbitmq:publishMessage', queueName, message),
    consumeMessages: queueName => ipcRenderer.invoke('rabbitmq:consumeMessages', queueName),
    sendSale: saleId => ipcRenderer.invoke('rabbitmq:sendSale', saleId),
    sendRefund: refundId => ipcRenderer.invoke('rabbitmq:sendRefund', refundId),
    sendEmployeeUpdate: employeeData =>
      ipcRenderer.invoke('rabbitmq:sendEmployeeUpdate', employeeData),
    triggerSync: categories => ipcRenderer.invoke('rabbitmq:triggerSync', categories),
    getSyncStats: () => ipcRenderer.invoke('rabbitmq:getSyncStats'),
    getFailedSyncs: () => ipcRenderer.invoke('rabbitmq:getFailedSyncs'),

    // Event listeners for messages
    onMessageReceived: callback => ipcRenderer.on('rabbitmq:messageReceived', callback),
    removeMessageListener: callback =>
      ipcRenderer.removeListener('rabbitmq:messageReceived', callback),
  },

  // Sales update listener
  onSalesUpdate: callback => ipcRenderer.on('sales-update', (_, data) => callback(data)),

  // HTTP request operations via IPC
  https: {
    request: ({ hostname, port, path, method, payload }) =>
      ipcRenderer.invoke('https:request', { hostname, port, path, method, payload }),
  },

  // Application control operations via IPC
  closeApp: () => ipcRenderer.invoke('app:close'),
  openCustomerWindow: () => ipcRenderer.invoke('app:openCustomerWindow'),
  updateCustomerScreen: salesItems => ipcRenderer.invoke('app:updateCustomerScreen', salesItems),
}

// Expose secure API to renderer process via contextBridge
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

console.log('✅ Secure preload script completed - electronAPI exposed via contextBridge')
