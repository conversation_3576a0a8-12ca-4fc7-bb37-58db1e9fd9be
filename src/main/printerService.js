// Gerekli kütüphaneleri simüle edelim veya varsayalım
// Bu örnek Node.js ortamında çalışmaya uygundur.
// Browser ortamında 'Buffer' ve 'cp857' encoding desteği farklılık gösterebilir.
// Eğer gerçekten cp857 encoding gerekiyorsa, Node.js için 'iconv-lite' kurulup kullanılmalıdır.
// const iconv = require('iconv-lite'); // Gerçek bir senaryoda bu gerekli olabilir
// const fs = require('fs'); // Gerçek yazıcı etkileşimi için gerekli
// const db = { // Basit bir db simülasyonu
//     prepare: () => ({
//         get: () => ({ printer_path: '/dev/usb/lp0' }),
//         run: () => {}
//     })
// };

// --- TypeScript Arayüzleri ve Enum'lar (JS JSDoc olarak) ---

/**
 * @typedef {object} IUrun
 * @property {string} barkod
 * @property {string} ad
 * @property {number} fiyat
 * @property {number} [kdvOrani]
 * @property {number} [weight]
 * @property {string} [weight_unit]
 * @property {number} [quantity]
 */

/**
 * @typedef {object} CardPayments
 * @property {string} [totalAmount]
 * @property {string} [acquirer]
 * @property {string} [acquirerReference]
 * @property {string} [date]
 * @property {string} [time]
 * @property {string} [cardNo]
 */

/**
 * @typedef {object} IReceiptData
 * @property {ReceiptType} type
 * @property {string} [marketAdi]
 * @property {string} [subeAdi]
 * @property {string} [adres]
 * @property {string} [ilceSehir]
 * @property {string} [telefon]
 * @property {string} [website]
 * @property {string} [vergiDairesiNo]
 * @property {string} tarih
 * @property {string} saat
 * @property {string} fisNo
 * @property {string} [kullanıcıAdı]
 * @property {string} [ekuno]
 * @property {string} [mersis]
 * @property {string} [ubNo]
 * @property {string} [paraUstu]
 * @property {IUrun[]} urunler
 * @property {{ type: string }[]} [payment_types]
 * @property {CardPayments} [cardPayments]
 */

/**
 * @enum {string}
 */
const ReceiptType = {
  SATIS: 'SATIS',
  IADE: 'IADE',
}

/**
 * @typedef {object} Market
 * @property {string} [name]
 * @property {string} [address]
 * @property {string} [district]
 * @property {string} [city]
 * @property {string} [phone_number]
 * @property {string} [tax_number]
 */

// Ödeme metodlarının etiketleri (genellikle bir ENUM veya MAP'ten gelir)
const PaymentMethodLabels = {
  CASH: 'NAKİT',
  CARD: 'KART',
  // Diğer ödeme türleri buraya eklenebilir
}

// --- Sabitler ve Yardımcı Fonksiyonlar ---
const PRINTER_ENCODING = 'cp857'
const ESC = '\x1B'
const GS = '\x1D'
const LF = '\x0A' // Line Feed

/**
 * Verilen komutu (string) Uint8Array'e ekler, belirli bir kodlamayla.
 * @param {Uint8Array} commands - Mevcut komutların Uint8Array'i.
 * @param {string} command - Eklenecek string komut.
 * @returns {Uint8Array} Yeni komutlar dizisi.
 */
function appendCommand(commands, command) {
  // Node.js'de Buffer.from direkt olarak 'cp857' gibi kodlamaları destekler.
  // Eğer desteklemiyorsa, iconv-lite kullanılmalıdır: iconv.encode(command, PRINTER_ENCODING)
  const encodedCommand = Buffer.from(command, PRINTER_ENCODING)
  const newCommands = new Uint8Array(commands.length + encodedCommand.length)
  newCommands.set(commands)
  newCommands.set(encodedCommand, commands.length)
  return newCommands
}

/**
 * Sayısal bir değeri para birimi formatına dönüştürür.
 * @param {number} amount - Formatlanacak miktar.
 * @returns {string} Formatlanmış string.
 */
function formatCurrency(amount) {
  return amount
    .toFixed(2)
    .replace('.', ',')
    .replace(/\B(?=(\d{3})+(?!\d))/g, '.')
}

/**
 * Satış fişi layout'unu oluşturur.
 * @param {IReceiptData} receiptData - Fiş verileri.
 * @param {Uint8Array} commands - Mevcut komutlar dizisi.
 * @param {Market} marketDetails - Pazar yeri bilgileri.
 * @returns {Uint8Array} Güncellenmiş komutlar dizisi.
 */
function generateSatisLayout(receiptData, commands, marketDetails) {
  // Market Bilgileri
  commands = appendCommand(commands, `Furpa${LF}`)
  if (marketDetails.name) commands = appendCommand(commands, `${marketDetails.name}${LF}`)
  if (marketDetails.address) commands = appendCommand(commands, `${marketDetails.address}${LF}`)
  if (marketDetails.district && marketDetails.city)
    commands = appendCommand(commands, `${marketDetails.district} - ${marketDetails.city}${LF}`)
  if (marketDetails.phone_number)
    commands = appendCommand(commands, `${marketDetails.phone_number}${LF}`)
  commands = appendCommand(commands, `https://www.furpa.com${LF}`)
  if (marketDetails.tax_number)
    commands = appendCommand(commands, `${marketDetails.tax_number}${LF}`)

  commands = appendCommand(commands, LF)

  // Tarih ve Saat
  commands = appendCommand(
    commands,
    `Tarih: ${receiptData.tarih}                   Saat: ${receiptData.saat}${LF}`
  )
  commands = appendCommand(commands, `Fiş No: ${receiptData.fisNo}${LF}${LF}`)

  commands = appendCommand(commands, `                                               ${LF}`)

  commands = appendCommand(commands, `ÜRÜNLER                MİKTAR    KDV      TUTAR${LF}`)

  commands = appendCommand(commands, `-----------------------------------------------${LF}`)

  // Ürünler Bölümü
  for (const urun of receiptData.urunler) {
    // Kolon genişlikleri
    const URUN_ADI_GENISLIK = 20
    const MIKTAR_GENISLIK = 8
    const KDV_GENISLIK = 2 // Bu aslında "%KDV" formatı için yer, metin uzunluğu olarak değil.
    const BOSLUK_GENISLIK = 4
    const TUTAR_GENISLIK = 12

    const urunAdi =
      urun.ad.length > URUN_ADI_GENISLIK ? urun.ad.substring(0, URUN_ADI_GENISLIK) : urun.ad
    const kdvOrani = urun.kdvOrani
    // Burada fiyat hesaplamasına dikkat: weight varsa quantity yok, yoksa quantity var.
    const birimFiyatKDVli = urun.fiyat * (urun.weight ? 1 : urun.quantity || 0) // quantity 0 olabilir
    const satirToplam = birimFiyatKDVli

    // Her kolonu sabit genişlikte formatla
    const urunAdiKolon = urunAdi.padEnd(URUN_ADI_GENISLIK, ' ')
    const miktarKolon = (
      urun.weight ? `${urun.weight}${urun.weight_unit ?? 'gr'}` : `${urun.quantity}x`
    ).padStart(MIKTAR_GENISLIK, ' ')
    const kdvKolon = `%${kdvOrani || 0}`.padEnd(KDV_GENISLIK, ' ') // KDV oranı belirtilmemişse 0 varsayalım
    const tutarKolon = formatCurrency(satirToplam).padStart(TUTAR_GENISLIK, ' ')

    // Kolonları birleştir (boşluklar sabit)
    commands = appendCommand(
      commands,
      `${urunAdiKolon}${miktarKolon}${' '.repeat(BOSLUK_GENISLIK)}${kdvKolon}${tutarKolon}${LF}`
    )
  }

  commands = appendCommand(commands, `-----------------------------------------------${LF}`)

  commands = appendCommand(commands, `                                               ${LF}`)

  // Toplam Hesaplamaları
  const kdvToplam = receiptData.urunler.reduce((sum, urun) => {
    const itemPrice = urun.fiyat * (urun.weight ? 1 : urun.quantity || 0)
    const kdvAmount = urun.kdvOrani
      ? (itemPrice / (1 + urun.kdvOrani / 100)) * (urun.kdvOrani / 100)
      : 0
    return sum + kdvAmount
  }, 0)

  const genelToplam = receiptData.urunler.reduce(
    (sum, urun) => sum + urun.fiyat * (urun.weight ? 1 : urun.quantity || 0),
    0
  )

  if (receiptData?.payment_types && receiptData?.payment_types?.length > 0) {
    commands = appendCommand(
      commands,
      `Ödeme Türü: ${receiptData.payment_types.every(type => type.type === receiptData.payment_types[0].type) ? PaymentMethodLabels[receiptData.payment_types[0].type] : 'Çoklu'}${LF}`
    )
  }

  kdvToplam &&
    (commands = appendCommand(
      commands,
      `KDV:                                      ${formatCurrency(kdvToplam).padStart(TUTAR_GENISLIK)}${LF}`
    ))
  commands = appendCommand(
    commands,
    `TOPLAM:                                  ${formatCurrency(genelToplam).padStart(TUTAR_GENISLIK)}${LF}`
  )
  commands = appendCommand(commands, `-----------------------------------------------${LF}`)

  if (receiptData.cardPayments) {
    receiptData.cardPayments.acquirer &&
      (commands = appendCommand(
        commands,
        `KARTLA ÖDEME:                         ${receiptData.cardPayments.totalAmount}${LF}`
      ))
    receiptData.cardPayments.acquirer &&
      (commands = appendCommand(commands, `${receiptData.cardPayments.acquirer}${LF}`))
    receiptData.cardPayments.acquirerReference &&
      (commands = appendCommand(commands, `${receiptData.cardPayments.acquirerReference}${LF}`))
    receiptData.cardPayments.date &&
      (commands = appendCommand(
        commands,
        `${receiptData.cardPayments.date} ${receiptData.cardPayments.time}${LF}`
      ))
    receiptData.cardPayments.cardNo &&
      (commands = appendCommand(commands, `${receiptData.cardPayments.cardNo}${LF}`))

    receiptData.cardPayments.acquirer &&
      (commands = appendCommand(commands, `-----------------------------------------------${LF}`))
  }

  return commands
}

/**
 * İade fişi layout'unu oluşturur.
 * @param {IReceiptData} receiptData - Fiş verileri.
 * @param {Uint8Array} commands - Mevcut komutlar dizisi.
 * @param {Market} marketDetails - Pazar yeri bilgileri.
 * @returns {Uint8Array} Güncellenmiş komutlar dizisi.
 */
function generateIadeLayout(receiptData, commands, marketDetails) {
  commands = appendCommand(commands, `Furpa${LF}`)
  if (marketDetails.name) commands = appendCommand(commands, `${marketDetails.name}${LF}`)
  if (marketDetails.address) commands = appendCommand(commands, `${marketDetails.address}${LF}`)
  if (marketDetails.district && marketDetails.city)
    commands = appendCommand(commands, `${marketDetails.district} - ${marketDetails.city}${LF}`)
  if (marketDetails.phone_number)
    commands = appendCommand(commands, `${marketDetails.phone_number}${LF}`)
  commands = appendCommand(commands, `https://www.furpa.com${LF}`)
  if (marketDetails.tax_number)
    commands = appendCommand(commands, `${marketDetails.tax_number}${LF}`)

  commands = appendCommand(commands, LF)

  // Tarih ve Saat
  commands = appendCommand(commands, `GİDER PUSULASI${LF}`)
  commands = appendCommand(commands, `${LF}`)
  commands = appendCommand(
    commands,
    `Tarih: ${receiptData.tarih}                   Saat: ${receiptData.saat}${LF}`
  )

  commands = appendCommand(commands, `Fiş No: ${receiptData.fisNo}${LF}${LF}`)

  commands = appendCommand(commands, `ÜRÜNLER                      MİKTAR       TUTAR${LF}`)

  commands = appendCommand(commands, `-----------------------------------------------${LF}`)

  // Ürünler Bölümü
  for (const urun of receiptData.urunler) {
    // Kolon genişlikleri
    const URUN_ADI_GENISLIK = 25
    const MIKTAR_GENISLIK = 8
    const BOSLUK_GENISLIK = 3
    const TUTAR_GENISLIK = 12

    const urunAdi =
      urun.ad.length > URUN_ADI_GENISLIK ? urun.ad.substring(0, URUN_ADI_GENISLIK) : urun.ad
    const satirToplam = urun.fiyat * (urun.weight ? 1 : urun.quantity || 0)

    // Her kolonu sabit genişlikte formatla
    const urunAdiKolon = urunAdi.padEnd(URUN_ADI_GENISLIK, ' ')
    const miktarKolon = (
      urun.weight ? `${urun.weight}${urun.weight_unit ?? 'gr'}` : `${urun.quantity}x`
    ).padStart(MIKTAR_GENISLIK, ' ')
    const tutarKolon = formatCurrency(satirToplam).padStart(TUTAR_GENISLIK, ' ')

    // Kolonları birleştir (boşluklar sabit)
    commands = appendCommand(
      commands,
      `${urunAdiKolon}${miktarKolon}${' '.repeat(BOSLUK_GENISLIK)}${tutarKolon}${LF}`
    )
  }

  commands = appendCommand(commands, `-----------------------------------------------${LF}`)

  // Toplam Hesaplamaları
  const genelToplam = receiptData.urunler.reduce(
    (sum, urun) => sum + urun.fiyat * (urun.weight ? 1 : urun.quantity || 0),
    0
  )

  commands = appendCommand(
    commands,
    `İADE TOPLAM:                           ${formatCurrency(genelToplam).padStart(TUTAR_GENISLIK)}${LF}`
  )
  commands = appendCommand(commands, `-----------------------------------------------${LF}`)

  return commands
}

/**
 * Fiş verilerini alıp yazıcıya gönderilecek ham Uint8Array komutlarını oluşturur.
 * Bu fonksiyon, bir yazıcı hizmetinin çekirdek "fiş oluşturma" mantığını içerir.
 *
 * @param {IReceiptData} receiptData - Fiş için gerekli verileri içeren obje.
 * @param {object} [options] - Opsiyonel ayarlar.
 * @param {boolean} [options.isCopy=false] - Fişin bir kopya olup olmadığını belirtir.
 * @param {Market} [options.marketDetails] - Markete ait özel bilgiler. Verilmezse varsayılan bilgiler kullanılır.
 * @returns {Uint8Array} Yazıcıya gönderilmeye hazır Uint8Array formatındaki komutlar.
 */
function generatePrinterCommands(receiptData, options = {}) {
  let commands = new Uint8Array([])
  const {
    isCopy = false,
    marketDetails = {
      // Varsayılan market bilgileri
      name: 'Furpa Market',
      address: 'Örnek Cad. No:123, Örnek Mah.',
      district: 'Kadıköy',
      city: 'İstanbul',
      phone_number: '0216 123 45 67',
      tax_number: '1234567890',
    },
  } = options

  // Yazıcı başlangıç komutları
  commands = appendCommand(commands, `${ESC}@`) // Initialize printer
  commands = appendCommand(commands, `${ESC}t\x3D`) // Code Page Selection (ESC t n, n=61 for cp857)
  // GS ( C 02 00 03 -- Karakter Seti Seçimi (Genellikle GS ( k pL pH cn fn ) veya ESC t n şeklindedir)
  // Buradaki komut (GS(C\x02\x00\x03) ESC/POS standardında karakter seti için değil,
  // farklı bir komut olabilir veya spesifik bir yazıcı modeline aittir.
  // ESC t 0x3D (decimal 61) = cp857 için yaygın bir koddur.
  commands = appendCommand(commands, `${ESC}a\x01`) // Align text to center

  isCopy && (commands = appendCommand(commands, `BELGE KOPYASI${LF}`))

  // Layout seçimi
  if (receiptData.type === ReceiptType.IADE) {
    commands = generateIadeLayout(receiptData, commands, marketDetails)
  } else {
    commands = generateSatisLayout(receiptData, commands, marketDetails)
  }

  // Alt Bilgiler
  if (receiptData.paraUstu)
    commands = appendCommand(
      commands,
      `PARA ÜSTÜ: ${formatCurrency(parseFloat(receiptData.paraUstu))}${LF}`
    )
  if (receiptData.kullanıcıAdı)
    commands = appendCommand(commands, `${receiptData.kullanıcıAdı}${LF}`)

  commands = appendCommand(commands, `*** TEŞEKKÜRLER ***${LF}`)

  if (receiptData.mersis) commands = appendCommand(commands, `${receiptData.mersis}${LF}`)
  if (receiptData.ubNo) commands = appendCommand(commands, `${receiptData.ubNo}${LF}`)

  isCopy && (commands = appendCommand(commands, `BELGE KOPYASI`))

  // 5 satır boşluk ve kesme komutu
  commands = appendCommand(commands, `${LF}${LF}${LF}${LF}${LF}`)
  // GS V 1 (Full cut) komutu
  commands = new Uint8Array([...commands, ...new Uint8Array([GS.charCodeAt(0), 86, 1])])

  return commands
}

// --- Fonksiyonun Dışa Aktarılması ---
// Eğer bu bir Node.js modülü ise:
module.exports = {
  generatePrinterCommands,
  ReceiptType,
  // Diğer yardımcı fonksiyonları da dışa aktarabilirsiniz eğer dışarıdan erişim gerekiyorsa
  // appendCommand,
  // formatCurrency,
}

// --- KULLANIM ÖRNEĞİ ---
if (require.main === module) {
  // Sadece dosya doğrudan çalıştırıldığında çalışır
  const sampleReceiptData = {
    type: ReceiptType.SATIS,
    tarih: '2023-11-20',
    saat: '14:30',
    fisNo: 'Fis-20231120-001',
    kullanıcıAdı: 'Deneme Kullanıcı',
    urunler: [
      { barkod: '12345', ad: 'Elma', fiyat: 15.5, kdvOrani: 8, quantity: 2 },
      { barkod: '67890', ad: 'Ekmek', fiyat: 8.75, kdvOrani: 1, quantity: 1 },
      { barkod: '11223', ad: 'Peynir', fiyat: 45.0, kdvOrani: 8, weight: 0.5, weight_unit: 'kg' },
      { barkod: '44556', ad: 'Su (500ml)', fiyat: 2.5, kdvOrani: 8, quantity: 6 },
      { barkod: '77889', ad: 'Çikolata', fiyat: 12.0, kdvOrani: 18, quantity: 1 },
    ],
    payment_types: [{ type: 'CASH' }],
    paraUstu: '50.00',
    mersis: 'MERSIS: 0123456789012345',
    ubNo: 'UB No: 987654321',
  }

  const sampleReturnReceiptData = {
    type: ReceiptType.IADE,
    tarih: '2023-11-20',
    saat: '15:00',
    fisNo: 'IADE-20231120-001',
    urunler: [
      { barkod: '12345', ad: 'Elma', fiyat: 15.5, kdvOrani: 8, quantity: 1 },
      { barkod: '67890', ad: 'Ekmek', fiyat: 8.75, kdvOrani: 1, quantity: 1 },
    ],
  }

  console.log('--- SATIŞ FİŞİ KOMUTLARI ---')
  const salesCommands = generatePrinterCommands(sampleReceiptData)
  // Uint8Array'i string olarak görmek için (örneğin UTF-8 veya cp857 ile çözülmüş):
  console.log(Buffer.from(salesCommands).toString(PRINTER_ENCODING))
  // Veya ham byte'ları görmek için:
  // console.log(salesCommands);

  console.log('\n--- İADE FİŞİ KOMUTLARI ---')
  const returnCommands = generatePrinterCommands(sampleReturnReceiptData)
  console.log(Buffer.from(returnCommands).toString(PRINTER_ENCODING))
}
