/**
 * Application Control IPC Handlers
 * Handles application-level IPC communication
 */

import { app, ipcMain } from 'electron'
import rabbitmqService from '../rabbitmq.js'

// Customer window fonksiyonunu import et
let createCustomerWindow
let customerWindow

export function setCreateCustomerWindow(fn) {
  createCustomerWindow = fn
}

export function setCustomerWindow(window) {
  customerWindow = window
}

export function appHandlers() {
  // Close application
  ipcMain.handle('app:close', async () => {
    try {
      console.log('🚪 IPC: Application close requested')

      // Disconnect from RabbitMQ before closing
      try {
        await rabbitmqService.disconnect()
        console.log('✅ RabbitMQ disconnected successfully')
      } catch (error) {
        console.error('⚠️ Error disconnecting RabbitMQ:', error)
      }

      // Close the application
      app.quit()
      return { success: true, message: 'Application closing...' }
    } catch (error) {
      console.error('❌ IPC Error - app:close:', error)
      throw error
    }
  })

  // Open customer window
  ipcMain.handle('app:openCustomerWindow', async () => {
    try {
      console.log('🪟 IPC: Customer window open requested')

      if (createCustomerWindow) {
        createCustomerWindow()
        return { success: true, message: 'Customer window opened' }
      } else {
        console.error('❌ createCustomerWindow function not available')
        return { success: false, message: 'Customer window function not available' }
      }
    } catch (error) {
      console.error('❌ IPC Error - app:openCustomerWindow:', error)
      throw error
    }
  })

  // Update customer screen
  ipcMain.handle('app:updateCustomerScreen', async (_, salesItems) => {
    try {
      console.log('📊 IPC: Customer screen update requested')

      if (customerWindow && !customerWindow.isDestroyed()) {
        customerWindow.webContents.send('sales-update', salesItems)
        return { success: true, message: 'Customer screen updated' }
      } else {
        return { success: false, message: 'Customer window not available' }
      }
    } catch (error) {
      console.error('❌ IPC Error - app:updateCustomerScreen:', error)
      throw error
    }
  })

  console.log('✅ Application IPC handlers registered')
}
