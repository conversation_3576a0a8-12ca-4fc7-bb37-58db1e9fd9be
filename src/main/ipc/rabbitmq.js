/**
 * RabbitMQ IPC Handlers
 * Handles all RabbitMQ-related IPC communication
 */

import { ipcMain } from 'electron'
import Mq<PERSON><PERSON>pter, {
  getConsumerStatus,
  retryConsumerSetup,
  sendEmployeeUpdate,
  sendRefund,
  sendSale,
  testLocalEmployeeSync,
} from '../rabbitmq.js'
import { sendManualSync } from '../rabbitmq/outgoing/sync.js'
import SyncMonitor from '../rabbitmq/utils/syncMonitor.js'

export function rabbitmqHandlers() {
  ipcMain.handle('rabbitmq:getStatus', async () => {
    return {
      connected: !!MqAdapter.connection,
      consumers: MqAdapter.activeConsumers.size,
      activeConsumers: getConsumerStatus(),
      channelPoolStats: MqAdapter.channelPool?.getStats() || null,
    }
  })

  ipcMain.handle('rabbitmq:sendSale', async (_, saleId) => {
    try {
      return await sendSale(saleId)
    } catch (error) {
      throw new Error(`Failed to send sale: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:sendRefund', async (_, refundId) => {
    try {
      return await sendRefund(refundId)
    } catch (error) {
      throw new Error(`Failed to send refund: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:sendEmployeeUpdate', async (_, employeeData) => {
    try {
      return await sendEmployeeUpdate(employeeData)
    } catch (error) {
      throw new Error(`Failed to send employee update: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:triggerSync', async (_, categories = []) => {
    try {
      return await sendManualSync(categories)
    } catch (error) {
      throw new Error(`Failed to trigger sync: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:getSyncStats', async () => {
    try {
      return SyncMonitor.getSyncStats()
    } catch (error) {
      throw new Error(`Failed to get sync stats: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:getFailedSyncs', async () => {
    try {
      return SyncMonitor.getFailedSyncs()
    } catch (error) {
      throw new Error(`Failed to get failed syncs: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:retryConsumers', async () => {
    try {
      await retryConsumerSetup()
      return { success: true, message: 'Consumer setup retry completed' }
    } catch (error) {
      throw new Error(`Failed to retry consumer setup: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:getConsumers', async () => {
    try {
      return getConsumerStatus()
    } catch (error) {
      throw new Error(`Failed to get consumer status: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:testConsumerSetup', async () => {
    try {
      return await MqAdapter.testConsumerSetup()
    } catch (error) {
      throw new Error(`Failed to test consumer setup: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:getDetailedChannelStats', async () => {
    try {
      if (MqAdapter.channelPool) {
        return MqAdapter.channelPool.getDetailedStats()
      }
      return { error: 'Channel pool not available' }
    } catch (error) {
      throw new Error(`Failed to get detailed channel stats: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:forceInit', async () => {
    try {
      const { forceRabbitMQInit } = await import('../rabbitmq.js')
      return await forceRabbitMQInit()
    } catch (error) {
      throw new Error(`Failed to force init RabbitMQ: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:checkQueues', async () => {
    try {
      const { checkQueueStatus } = await import('../rabbitmq.js')
      return await checkQueueStatus()
    } catch (error) {
      throw new Error(`Failed to check queue status: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:testEmployeeQueue', async () => {
    try {
      const { testEmployeeQueue } = await import('../rabbitmq.js')
      return await testEmployeeQueue()
    } catch (error) {
      throw new Error(`Failed to test employee queue: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:forceEmployeeFullSync', async () => {
    try {
      const { forceEmployeeFullSync } = await import('../rabbitmq.js')
      return await forceEmployeeFullSync()
    } catch (error) {
      throw new Error(`Failed to force employee full sync: ${error.message}`)
    }
  })

  ipcMain.handle('rabbitmq:testLocalEmployeeSync', async () => {
    try {
      return await testLocalEmployeeSync()
    } catch (error) {
      throw new Error(`Failed to test local employee sync: ${error.message}`)
    }
  })
}
