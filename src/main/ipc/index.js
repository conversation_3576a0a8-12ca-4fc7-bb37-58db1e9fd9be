/**
 * Centralized IPC Handler Manager
 * Organizes all IPC communication between main and renderer processes
 */

import { ipcMain } from 'electron'
import { appHandlers } from './app.js'
import { authHandlers } from './auth.js'
import { configHandlers } from './config.js'
import { databaseHandlers } from './database.js'
import { dbTools } from './dbTools.js'
import { httpHandlers } from './http.js'
import { rabbitmqHandlers } from './rabbitmq.js'

/**
 * Setup all IPC handlers for secure communication
 * between main and renderer processes
 */
export function setupIpcHandlers() {
  console.log('🔧 Setting up IPC handlers...')

  // Database operations
  databaseHandlers()

  // Authentication operations
  authHandlers()

  // Configuration operations
  configHandlers()

  // RabbitMQ operations
  rabbitmqHandlers()

  // HTTP request operations
  httpHandlers()

  // Application control operations
  appHandlers()

  // Database tools operations
  dbTools()

  console.log('✅ IPC handlers setup complete')
}

/**
 * Cleanup all IPC handlers
 */
export function cleanupIpcHandlers() {
  console.log('🧹 Cleaning up IPC handlers...')
  ipcMain.removeAllListeners()
  console.log('✅ IPC handlers cleaned up')
}
