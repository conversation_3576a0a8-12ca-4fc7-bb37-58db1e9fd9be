import dotenv from 'dotenv'
import { ipcMain } from 'electron'
import pkg from 'pg'
import { getDatabase } from '../database.js'
const { Pool } = pkg

// .env dosyasındaki değişkenleri yükle
dotenv.config()

function dbTools() {
  // Inventory Database Sync Handler
  ipcMain.handle('syncInventoryDB', async (_event, ipNo) => {
    try {
      // PostgreSQL bağlantı bilgileri
      const connectionConfig = {
        user: process.env.POSTGRES_USER,
        host: ipNo,
        database: process.env.POSTGRES_DB,
        password: process.env.POSTGRES_PASSWORD,
        port: parseInt(process.env.POSTGRES_PORT || '5432'),
        ssl: process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false,
        // Bağlantı timeout değeri (10 saniye)
        connectionTimeoutMillis: 5000,
      }

      console.log(`PostgreSQL bağlantısı oluşturuluyor... IP: ${connectionConfig.host}`)

      // Promise.race kullanarak timeout ekle
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(
            new Error('PostgreSQL bağlantı zaman aşımı - 10 saniye içinde bağlantı kurulamadı.')
          )
        }, 10000)
      })

      // PostgreSQL bağlantı havuzu oluştur
      const pool = new Pool(connectionConfig)

      // İlk bağlantıyı test et - timeout kontrolü için
      const connectPromise = (async () => {
        const client = await pool.connect()
        try {
          // Basit bir sorgu çalıştır
          await client.query('SELECT 1')
          console.log('PostgreSQL bağlantısı başarıyla kuruldu.')
          return pool
        } finally {
          client.release()
        }
      })()

      // Hangisi önce gerçekleşirse: bağlantı veya timeout
      const connectedPool = await Promise.race([connectPromise, timeoutPromise])

      await seedMarket(connectedPool)

      await seedPaymentMethods()

      await seedDiscountTypes()

      await seedSaleStatuses()

      await seedSyncStatus()

      await seedPaymentProcessors()

      await getInventoryRecordsAndInsert(connectedPool)

      await getBarkodTanimlariAndInsert(connectedPool)

      await getStokSatisFiyatlariAndInsert(connectedPool)

      const result = await getCategoryAndInsert(connectedPool)

      await connectedPool.end()

      return result
    } catch (error) {
      console.error('Senkronizasyon hatası:', error)

      // Timeout hatası mı kontrol et
      const isTimeoutError =
        error.message.includes('zaman aşımı') ||
        error.message.includes('timeout') ||
        error.message.includes('ETIMEDOUT')

      return {
        success: false,
        message: isTimeoutError
          ? 'PostgreSQL bağlantısı 10 saniye içinde kurulamadı.'
          : 'Senkronizasyon başarısız oldu.',
        error: error.message,
      }
    }
  })

  // PostgreSQL'den tüm inventory kayıtlarını alıp SQLite'a aktaran fonksiyon
  async function getInventoryRecordsAndInsert(pool) {
    let client = null
    const CHUNK_SIZE = 100 // Her seferde kaç kayıt işlenecek
    let processedCount = 0
    let offset = 0
    let hasMoreRecords = true

    try {
      // PostgreSQL bağlantısı kur
      client = await pool.connect()

      // Toplam kayıt sayısını öğren
      const countResult = await client.query('SELECT COUNT(*) as total FROM inventory')
      const totalRecords = parseInt(countResult.rows[0].total)

      console.log(
        `Toplam ${totalRecords} adet inventory kaydı bulundu. ${CHUNK_SIZE}'li gruplar halinde aktarılacak.`
      )

      // Kayıtları CHUNK_SIZE'lık gruplar halinde işle
      while (hasMoreRecords) {
        // Belirli sayıda kayıt çek
        const query = `
        SELECT inventory_code, name, unit, tax_percent, ctg_code, resim
        FROM inventory
        ORDER BY inventory_code
        LIMIT ${CHUNK_SIZE} OFFSET ${offset}
      `

        const res = await client.query(query)

        // Eğer kayıt gelmezse döngüyü sonlandır
        if (res.rows.length === 0) {
          hasMoreRecords = false
          break
        }

        // Her bir kaydı SQLite'a ekle
        for (const inventory of res.rows) {
          // SQLite'a ekle
          const database = getDatabase()
          database
            .prepare(
              `
          INSERT INTO inventory (inventory_code, name, unit, tax_percent, ctg_code, resim)
          VALUES (?, ?, ?, ?, ?, ?)
        `
            )
            .run(
              inventory.inventory_code,
              inventory.name,
              inventory.unit,
              inventory.tax_percent,
              inventory.ctg_code,
              inventory.resim // PostgreSQL'deki bytea, SQLite'da BLOB olarak aktarılıyor
            )
        }

        // İşlenen kayıt sayısını güncelle
        processedCount += res.rows.length
        console.log(
          `${processedCount}/${totalRecords} kayıt işlendi (${Math.round((processedCount / totalRecords) * 100)}%)`
        )

        // Bir sonraki grup için offset'i güncelle
        offset += CHUNK_SIZE
      }

      return {
        success: true,
        message: `Toplam ${processedCount} kayıt başarıyla PostgreSQL'den SQLite'a aktarıldı.`,
      }
    } catch (error) {
      console.error('Inventory aktarımı sırasında hata oluştu:', error)
      return {
        success: false,
        message: 'Veri aktarımı başarısız oldu.',
        error: error.message,
      }
    } finally {
      // Her zaman client'ı serbest bırak
      if (client) {
        client.release()
      }
    }
  }

  async function getBarkodTanimlariAndInsert(pool) {
    let client = null
    const CHUNK_SIZE = 100
    let processedCount = 0
    let offset = 0
    let hasMoreRecords = true

    try {
      client = await pool.connect()
      const countResult = await client.query('SELECT COUNT(*) as total FROM barkodtanimlari')
      const totalRecords = parseInt(countResult.rows[0].total)

      console.log(
        `Toplam ${totalRecords} adet barkod tanımı bulundu. ${CHUNK_SIZE}'li gruplar halinde aktarılacak.`
      )

      while (hasMoreRecords) {
        const query = `
        SELECT bar_stokkodu, bar_kodu, bar_birimpntr, birimadi,
               to_char(bar_lastup_date, 'YYYY-MM-DD HH24:MI:SS') as bar_lastup_date
        FROM barkodtanimlari
        ORDER BY bar_stokkodu
        LIMIT ${CHUNK_SIZE} OFFSET ${offset}
      `

        const res = await client.query(query)

        if (res.rows.length === 0) {
          hasMoreRecords = false
          break
        }

        for (const barkod of res.rows) {
          // NOT NULL kısıtlamasına uygun olarak null değerlerini varsayılan değerlerle değiştir
          const birimAdi = barkod.birimadi || 'Tanımsız' // NULL ise "Tanımsız" kullan

          // SQLite'a ekle
          const database = getDatabase()
          database
            .prepare(
              `
          INSERT INTO BarkodTanimlari (bar_stokkodu, bar_kodu, bar_birimpntr, birimAdi, bar_lastup_date)
          VALUES (?, ?, ?, ?, ?)
        `
            )
            .run(
              barkod.bar_stokkodu || null,
              barkod.bar_kodu || null,
              barkod.bar_birimpntr || 0, // NULL ise 0 kullan
              birimAdi, // NULL olamaz, "Tanımsız" kullanıyoruz
              barkod.bar_lastup_date || null // String formatında timestamp
            )
        }

        processedCount += res.rows.length
        console.log(
          `${processedCount}/${totalRecords} barkod tanımı işlendi (${Math.round((processedCount / totalRecords) * 100)}%)`
        )

        offset += CHUNK_SIZE
      }

      return {
        success: true,
        message: `Toplam ${processedCount} barkod tanımı başarıyla PostgreSQL'den SQLite'a aktarıldı.`,
      }
    } catch (error) {
      console.error('Barkod tanımları aktarımı sırasında hata oluştu:', error)
      return {
        success: false,
        message: 'Barkod tanımları aktarımı başarısız oldu.',
        error: error.message,
      }
    } finally {
      if (client) {
        client.release()
      }
    }
  }

  async function getStokSatisFiyatlariAndInsert(pool) {
    let client = null
    const CHUNK_SIZE = 100
    let processedCount = 0
    let offset = 0
    let hasMoreRecords = true

    try {
      client = await pool.connect()

      // Önce tablo adını doğrulayalım
      const tableCheckQuery = `
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name LIKE '%stok%fiyat%'
    `
      const tableCheck = await client.query(tableCheckQuery)
      const tableName = tableCheck.rows.length > 0 ? tableCheck.rows[0].table_name : null

      if (!tableName) {
        throw new Error('Stok satış fiyatları tablosu veritabanında bulunamadı')
      }

      console.log(`Bulduğumuz tablo adı: ${tableName}`)

      // Doğru tablo adını kullanarak toplam kayıt sayısını öğren
      const countResult = await client.query(`SELECT COUNT(*) as total FROM ${tableName}`)
      const totalRecords = parseInt(countResult.rows[0].total)

      console.log(
        `Toplam ${totalRecords} adet stok satış fiyatı bulundu. ${CHUNK_SIZE}'li gruplar halinde aktarılacak.`
      )

      // Sütunların varlığını kontrol edelim
      const columnCheckQuery = `
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = '${tableName}'
    `
      const columnCheck = await client.query(columnCheckQuery)
      const columns = columnCheck.rows.map(row => row.column_name)

      console.log(`Mevcut sütunlar: ${columns.join(', ')}`)

      // Bulduğumuz sütunlara göre dinamik sorgu oluşturalım
      const selectColumns = [
        'sdp_depo_no',
        'sfiyat_stokkod',
        'fiyati',
        'fiyat_tip_kodu',
        'sto_birim_ad',
        'sdp_satisdursun',
        'sdp_sipdursun',
        'sdp_malkabuldursun',
        'sfiyat_lastup_date',
        'sdp_lastup_date',
      ].filter(col => columns.includes(col))

      // Tarih sütunlarını düzgün biçimde seçelim
      const selectQuery = selectColumns
        .map(col => {
          if (col.includes('date')) {
            return `to_char(${col}, 'YYYY-MM-DD HH24:MI:SS') as ${col}`
          }
          return col
        })
        .join(', ')

      while (hasMoreRecords) {
        // Dinamik oluşturulmuş sorguyu kullan
        const query = `
        SELECT ${selectQuery}
        FROM ${tableName}
        ORDER BY sfiyat_stokkod, sdp_depo_no
        LIMIT ${CHUNK_SIZE} OFFSET ${offset}
      `

        const res = await client.query(query)

        if (res.rows.length === 0) {
          hasMoreRecords = false
          break
        }

        // Her bir kaydı SQLite'a ekle
        for (const fiyat of res.rows) {
          // Kayıt öncesi null değerleri kontrol edelim
          const values = [
            fiyat.sdp_depo_no !== undefined ? fiyat.sdp_depo_no : null,
            fiyat.sfiyat_stokkod !== undefined ? fiyat.sfiyat_stokkod : null,
            fiyat.fiyati !== undefined ? fiyat.fiyati : null,
            fiyat.fiyat_tip_kodu !== undefined ? fiyat.fiyat_tip_kodu : null,
            fiyat.sto_birim_ad !== undefined ? fiyat.sto_birim_ad : null,
            fiyat.sdp_satisdursun !== undefined ? fiyat.sdp_satisdursun : null,
            fiyat.sdp_sipdursun !== undefined ? fiyat.sdp_sipdursun : null,
            fiyat.sdp_malkabuldursun !== undefined ? fiyat.sdp_malkabuldursun : null,
            fiyat.sfiyat_lastup_date !== undefined ? fiyat.sfiyat_lastup_date : null,
            fiyat.sdp_lastup_date !== undefined ? fiyat.sdp_lastup_date : null,
          ]

          // SQLite'a ekle - burada tüm alanları hazırla
          const stmt = getDatabase().prepare(`
          INSERT INTO StokSatisFiyat (
            sdp_depo_no, sfiyat_stokkod, Fiyati, Fiyat_Tip_Kodu, sto_birim_ad,
            sdp_satisdursun, sdp_sipdursun, sdp_malkabuldursun,
            sfiyat_lastup_date, sdp_lastup_date
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `)

          stmt.run(...values)
        }

        processedCount += res.rows.length
        console.log(
          `${processedCount}/${totalRecords} stok satış fiyatı işlendi (${Math.round((processedCount / totalRecords) * 100)}%)`
        )

        offset += CHUNK_SIZE
      }

      return {
        success: true,
        message: `Toplam ${processedCount} stok satış fiyatı başarıyla PostgreSQL'den SQLite'a aktarıldı.`,
      }
    } catch (error) {
      console.error('Stok satış fiyatları aktarımı sırasında hata oluştu:', error)
      return {
        success: false,
        message: 'Stok satış fiyatları aktarımı başarısız oldu.',
        error: error.message,
      }
    } finally {
      if (client) {
        client.release()
      }
    }
  }

  async function getCategoryAndInsert(pool) {
    let client = null
    const CHUNK_SIZE = 100 // Her seferde kaç kayıt işlenecek
    let processedCount = 0
    let offset = 0
    let hasMoreRecords = true

    try {
      // PostgreSQL bağlantısı kur
      client = await pool.connect()

      // Toplam kayıt sayısını öğren
      const countResult = await client.query('SELECT COUNT(*) as total FROM category')
      const totalRecords = parseInt(countResult.rows[0].total)

      console.log(
        `Toplam ${totalRecords} adet kategori bulundu. ${CHUNK_SIZE}'li gruplar halinde aktarılacak.`
      )

      // Kayıtları CHUNK_SIZE'lık gruplar halinde işle
      while (hasMoreRecords) {
        // Belirli sayıda kayıt çek
        const query = `
        SELECT name, ctg_code
        FROM category
        ORDER BY ctg_code
        LIMIT ${CHUNK_SIZE} OFFSET ${offset}
      `

        const res = await client.query(query)

        // Eğer kayıt gelmezse döngüyü sonlandır
        if (res.rows.length === 0) {
          hasMoreRecords = false
          break
        }

        // Her bir kaydı SQLite'a ekle
        for (const category of res.rows) {
          // NOT NULL kısıtlamasına uygun olarak null değerlerini varsayılan değerlerle değiştir
          const name = category.name || 'Tanımsız Kategori' // NULL ise "Tanımsız Kategori" kullan
          const ctg_code = category.ctg_code || 'UNKNOWN' // NULL ise "UNKNOWN" kullan

          try {
            // SQLite'a ekle
            getDatabase()
              .prepare(
                `
            INSERT INTO category (name, ctg_code)
            VALUES (?, ?)
            `
              )
              .run(name, ctg_code)
          } catch (insertError) {
            // Eğer kategori kodu zaten varsa (UNIQUE kısıtlaması), güncelleme yapalım
            if (insertError.code === 'SQLITE_CONSTRAINT_UNIQUE') {
              console.log(`Kategori kodu zaten var, güncelleniyor: ${ctg_code}`)
              getDatabase()
                .prepare(
                  `
              UPDATE category
              SET name = ?
              WHERE ctg_code = ?
              `
                )
                .run(name, ctg_code)
            } else {
              // Başka bir hata ise yeniden fırlat
              throw insertError
            }
          }
        }

        // İşlenen kayıt sayısını güncelle
        processedCount += res.rows.length
        console.log(
          `${processedCount}/${totalRecords} kategori işlendi (${Math.round((processedCount / totalRecords) * 100)}%)`
        )

        // Bir sonraki grup için offset'i güncelle
        offset += CHUNK_SIZE
      }

      return {
        success: true,
        message: `Toplam ${processedCount} kategori başarıyla PostgreSQL'den SQLite'a aktarıldı.`,
      }
    } catch (error) {
      console.error('Kategori aktarımı sırasında hata oluştu:', error)
      return {
        success: false,
        message: 'Kategori aktarımı başarısız oldu.',
        error: error.message,
      }
    } finally {
      // Her zaman client'ı serbest bırak
      if (client) {
        client.release()
      }
    }
  }

  async function seedSaleStatuses() {
    const database = getDatabase()
    const insert = database.transaction(() => {
      const sale_statuses = [
        { name: 'in_progress' },
        { name: 'on_hold' },
        { name: 'canceled' },
        { name: 'completed' },
        { name: 'refunded' },
      ]

      const insertStatuses = database.prepare(`
      INSERT OR IGNORE INTO sale_statuses (name)
      VALUES (@name)
    `)

      for (const status of sale_statuses) {
        insertStatuses.run(status)
      }
    })

    insert()
  }

  async function seedPaymentProcessors() {
    const insert = getDatabase().transaction(() => {
      const payment_processors = [{ name: 'Pavo', is_active: 1, settings: '{}' }]

      const insertProcessors = getDatabase().prepare(`
      INSERT OR IGNORE INTO payment_processors (name, is_active, settings)
      VALUES (@name, @is_active, @settings)
    `)

      for (const processor of payment_processors) {
        insertProcessors.run(processor)
      }
    })

    insert()
  }

  async function seedSyncStatus() {
    const insert = getDatabase().transaction(() => {
      const sync_statuses = [
        { sync_type: 'employees', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'permissions', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'roles', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'fast-access', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'workstation-id', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'promotions', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'groups', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'banknotes', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'payment-methods', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'discount-types', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'market-id', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'display-content', last_sync: new Date().toISOString(), status: 'pending' },
        { sync_type: 'display-settings', last_sync: new Date().toISOString(), status: 'pending' },
      ]

      const insertStatuses = getDatabase().prepare(`
      INSERT OR IGNORE INTO sync_status (sync_type, last_sync, status, created_at, updated_at)
      VALUES (@sync_type, @last_sync, @status, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    `)

      for (const status of sync_statuses) {
        insertStatuses.run(status)
      }
    })

    insert()
  }

  async function seedDiscountTypes() {
    const insert = getDatabase().transaction(() => {
      const discount_types = [
        { name: 'none' },
        { name: 'fixed' },
        { name: 'percentage' },
        { name: 'customer' },
        { name: 'promotion' },
      ]

      const insertDiscountType = getDatabase().prepare(`
      INSERT OR IGNORE INTO discount_types (name)
      VALUES (@name)
    `)

      for (const type of discount_types) {
        insertDiscountType.run(type)
      }
    })

    insert()
  }

  async function seedPaymentMethods() {
    const insert = getDatabase().transaction(() => {
      const payment_methods = [
        { name: 'cash' },
        { name: 'credit_card' },
        { name: 'meal_card' },
        { name: 'single_use_card' },
        { name: 'premium_card' },
        { name: 'gift_card' },
      ]

      const insertPaymentMethod = getDatabase().prepare(`
      INSERT OR IGNORE INTO payment_methods (name)
      VALUES (@name)
    `)

      for (const method of payment_methods) {
        insertPaymentMethod.run(method)
      }
    })
    insert()
  }

  async function seedMarket(pool) {
    let client = null

    try {
      // PostgreSQL bağlantısı kur
      client = await pool.connect()

      // Doğrudan market_id tablosundan ilk kaydı çek
      const query = `SELECT * FROM market_id LIMIT 1`
      const res = await client.query(query)

      if (res.rows.length === 0) {
        throw new Error('PostgreSQL market_id tablosunda kayıt bulunamadı')
      }

      // PostgreSQL'den gelen veriyi al
      const marketData = res.rows[0]
      console.log("PostgreSQL'den alınan market verisi:", marketData)

      // SQLite'a update işlemi
      const updateQuery = `
      UPDATE market_id
      SET
        market_id = ?,
        name = ?,
        ip_address = ?,
        phone_number = ?,
        district = ?,
        city = ?,
        tax_number = ?
      WHERE id = 1
    `

      getDatabase()
        .prepare(updateQuery)
        .run(
          marketData.market_id || 'shopigo',
          marketData.name || 'Shopigo Market',
          marketData.ip_address || '127.0.0.1',
          marketData.phone_number || '************',
          marketData.district || 'İlçe',
          marketData.city || 'Bursa',
          marketData.tax_number || '11111111111'
        )

      console.log("Market bilgileri başarıyla PostgreSQL'den alındı ve SQLite'a aktarıldı.")
      return {
        success: true,
        message: `Market bilgileri başarıyla PostgreSQL'den alındı ve SQLite'a aktarıldı.`,
      }
    } catch (error) {
      console.error('Market bilgileri aktarımı sırasında hata oluştu:', error)

      return {
        success: false,
        message: "PostgreSQL'den market bilgileri alınamadı veya SQLite güncelleme başarısız oldu.",
        error: error.message,
      }
    } finally {
      // Her zaman client'ı serbest bırak
      if (client) {
        client.release()
      }
    }
  }
}

export { dbTools }
