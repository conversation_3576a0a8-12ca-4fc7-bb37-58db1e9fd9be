#!/usr/bin/env node

/**
 * Database schema creation script
 * This script creates all tables defined in schema.js
 */

import Database from 'better-sqlite3'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import { DB_CONFIG } from '../config.js'
import { applyDatabaseSchema } from '../schema.js'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

function createSchema() {
  try {
    console.log('🚀 Starting database schema creation...')

    // Create database directory if it doesn't exist
    const projectRoot = path.resolve(__dirname, '../../../')
    const dataDir = path.join(projectRoot, DB_CONFIG.path)

    // Ensure data directory exists
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true })
      console.log(`📁 Created data directory: ${dataDir}`)
    }

    // Create database file path
    const dbPath = path.join(dataDir, DB_CONFIG.name)
    console.log(`📁 Database path: ${dbPath}`)

    // Check if database already exists
    const dbExists = fs.existsSync(dbPath)
    if (dbExists) {
      console.log('📋 Database file already exists, applying schema...')
    } else {
      console.log('📋 Creating new database file...')
    }

    // Initialize database connection
    const db = new Database(dbPath)

    // Apply database schema from schema.js
    const success = applyDatabaseSchema(db)

    if (success) {
      console.log('✅ Database schema created/updated successfully!')
    } else {
      console.log('⚠️  Schema application completed with warnings')
    }

    // Close database connection
    db.close()
    console.log('🔐 Database connection closed')
  } catch (error) {
    console.error('❌ Error creating database schema:', error)
    process.exit(1)
  }
}

// Run the schema creation
createSchema()
