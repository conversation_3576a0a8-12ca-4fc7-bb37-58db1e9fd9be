import MqAdapter, { CONSUMER_TYPES } from './rabbitmq/adapter.js'
import { requestEmployeeFullSync } from './rabbitmq/incoming/sync.js'
import { sendEmployeeUpdate } from './rabbitmq/outgoing/employees.js'
import { sendRefund } from './rabbitmq/outgoing/refunds.js'
import { sendSale } from './rabbitmq/outgoing/sales.js'
import { startOutgoingPeriodicSync } from './rabbitmq/outgoing/sync.js'

// Export the main adapter and functions
export default MqAdapter
export { CONSUMER_TYPES, sendEmployeeUpdate, sendRefund, sendSale, startOutgoingPeriodicSync }

// Initialize the adapter
export async function initializeRabbitMQ() {
  try {
    console.log('[MQ] Initializing RabbitMQ...')
    await MqAdapter.connect()
    console.log('[MQ] Connection established')

    startOutgoingPeriodicSync()
    console.log('[MQ] Outgoing sync started')

    console.log('[MQ] RabbitMQ initialized successfully')
  } catch (error) {
    console.error('[MQ] Failed to initialize RabbitMQ:', error)
    throw error
  }
}

// Helper function to retry consumer setup
export async function retryConsumerSetup() {
  try {
    await MqAdapter.retrySetupConsumers()
    console.log('[MQ] Consumer setup retry completed')
  } catch (error) {
    console.error('[MQ] Consumer setup retry failed:', error)
    throw error
  }
}

// Helper function to get consumer status
export function getConsumerStatus() {
  return MqAdapter.getActiveConsumers()
}

// Shutdown function
export async function shutdownRabbitMQ() {
  try {
    await MqAdapter.shutdown()
    console.log('[MQ] RabbitMQ shutdown completed')
  } catch (error) {
    console.error('[MQ] Error during RabbitMQ shutdown:', error)
  }
}

// Helper function to force RabbitMQ initialization and debugging
export async function forceRabbitMQInit() {
  try {
    console.log('[MQ] Force initializing RabbitMQ...')

    // Force connect
    await MqAdapter.connect()
    console.log('[MQ] Connection established')

    // Check consumers
    const consumers = MqAdapter.getActiveConsumers()
    console.log('[MQ] Active consumers:', consumers)

    if (consumers.length === 0) {
      console.log('[MQ] No consumers found, retrying setup...')
      await MqAdapter.retrySetupConsumers()
    }

    // Request initial sync specifically for employees
    console.log('[MQ] Requesting employee sync...')
    await MqAdapter.withChannel(async channel => {
      await channel.assertQueue('sync-request', { durable: true })

      const syncRequest = {
        type: 'initial_sync',
        requested_at: new Date().toISOString(),
        categories: ['employees'],
        priority: 'high',
      }

      channel.sendToQueue('sync-request', Buffer.from(JSON.stringify(syncRequest)), {
        persistent: true,
      })

      console.log('[MQ] Employee sync request sent')
    })

    return { success: true, message: 'RabbitMQ force init completed' }
  } catch (error) {
    console.error('[MQ] Force init failed:', error)
    return { success: false, error: error.message }
  }
}

// Helper function to check queue status and message count
export async function checkQueueStatus() {
  try {
    return await MqAdapter.withChannel(async channel => {
      const queues = [
        'employees',
        'promotions',
        'fast-access',
        'groups',
        'display-changes',
        'price-updates',
        'sync-request',
        'sync-response',
      ]

      const queueStatus = {}

      for (const queueName of queues) {
        try {
          const queueInfo = await channel.checkQueue(queueName)
          queueStatus[queueName] = {
            exists: true,
            messageCount: queueInfo.messageCount,
            consumerCount: queueInfo.consumerCount,
          }
        } catch (error) {
          queueStatus[queueName] = {
            exists: false,
            error: error.message,
          }
        }
      }

      return queueStatus
    })
  } catch (error) {
    console.error('[MQ] Error checking queue status:', error)
    throw error
  }
}

// Helper function to manually trigger employee queue consumption
export async function testEmployeeQueue() {
  try {
    return await MqAdapter.withChannel(async channel => {
      await channel.assertQueue('employees', { durable: true })

      const queueInfo = await channel.checkQueue('employees')
      console.log('[MQ] Employee queue info:', queueInfo)

      if (queueInfo.messageCount > 0) {
        console.log(`[MQ] Found ${queueInfo.messageCount} messages in employee queue`)

        // Try to consume one message manually
        const msg = await channel.get('employees', { noAck: false })
        if (msg) {
          console.log('[MQ] Retrieved message from employee queue:', {
            messageSize: msg.content.length,
            routingKey: msg.fields?.routingKey,
            exchange: msg.fields?.exchange,
          })

          try {
            const data = JSON.parse(msg.content.toString())
            console.log('[MQ] Parsed employee message:', data)

            // Process the message (import from employee consumer)
            const { getDatabase } = await import('../database.js')
            const db = getDatabase()

            // Basic employee processing
            if (data.type === 'employee' && data.action && data.data) {
              if (data.action === 'create' || data.action === 'update') {
                const employee = data.data
                const stmt = db.prepare(`
                  INSERT OR REPLACE INTO users (
                    id, username, password, full_name, role,
                    created_at, updated_at, is_active
                  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `)

                stmt.run(
                  employee.id,
                  employee.username || employee.employee_code,
                  employee.password || '',
                  employee.full_name || employee.name,
                  employee.role || 'employee',
                  employee.created_at || new Date().toISOString(),
                  new Date().toISOString(),
                  employee.is_active !== false ? 1 : 0
                )

                console.log(`[MQ] Employee processed: ${employee.full_name || employee.name}`)
              }
            }

            // Acknowledge the message
            channel.ack(msg)

            return {
              success: true,
              processed: true,
              messageData: data,
              queueInfo,
            }
          } catch (parseError) {
            console.error('[MQ] Error parsing/processing message:', parseError)
            channel.nack(msg, false, false)
            return {
              success: false,
              error: parseError.message,
              queueInfo,
            }
          }
        } else {
          return {
            success: true,
            processed: false,
            message: 'No messages available for immediate consumption',
            queueInfo,
          }
        }
      } else {
        return {
          success: true,
          processed: false,
          message: 'Employee queue is empty',
          queueInfo,
        }
      }
    })
  } catch (error) {
    console.error('[MQ] Error testing employee queue:', error)
    return {
      success: false,
      error: error.message,
    }
  }
}

// Force employee full sync
export async function forceEmployeeFullSync() {
  try {
    console.log('[MQ] Forcing employee full sync...')

    // First ensure connection
    await MqAdapter.connect()

    // Request employee full sync
    const result = await requestEmployeeFullSync()

    // Also try to setup consumers if they're not running
    const consumers = MqAdapter.getActiveConsumers()
    if (consumers.length === 0) {
      console.log('[MQ] No consumers active, setting up consumers...')
      await MqAdapter.retrySetupConsumers()
    }

    return { success: true, message: 'Employee full sync forced', syncResult: result }
  } catch (error) {
    console.error('[MQ] Error forcing employee full sync:', error)
    return { success: false, error: error.message }
  }
}

// Test function for local employee sync
export async function testLocalEmployeeSync() {
  try {
    console.log('[MQ] Testing local employee sync...')

    // Ensure connection
    await MqAdapter.connect()

    // Import the request function
    const { requestEmployeeSync } = await import('./rabbitmq/incoming/localEmployees.js')

    // Request employee sync
    const result = await requestEmployeeSync()
    console.log('[MQ] Employee sync test result:', result)

    // Check consumer status
    const consumers = MqAdapter.getActiveConsumers()
    console.log(
      '[MQ] Active consumers:',
      consumers.map(c => c.type)
    )

    return {
      success: true,
      message: 'Local employee sync test completed',
      result,
      consumers: consumers.length,
    }
  } catch (error) {
    console.error('[MQ] Local employee sync test failed:', error)
    return { success: false, error: error.message }
  }
}
