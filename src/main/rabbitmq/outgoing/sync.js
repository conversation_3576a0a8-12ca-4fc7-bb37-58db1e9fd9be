import { getDatabase } from '../../database.js'
import MqAdapter from '../adapter.js'
import { sendPendingEmployeeUpdates } from './employees.js'
import { sendPendingRefunds } from './refunds.js'
import { sendPendingSales } from './sales.js'

export async function handleSalesDeltaSync() {
  try {
    await sendPendingSales()
    console.log('[MQ] [Sync] Sales delta sync completed')
  } catch (error) {
    console.error('[MQ] [Sync] Error in sales delta sync:', error)
    throw error
  }
}

export async function handleRefundsDeltaSync() {
  try {
    await sendPendingRefunds()
    console.log('[MQ] [Sync] Refunds delta sync completed')
  } catch (error) {
    console.error('[MQ] [Sync] Error in refunds delta sync:', error)
    throw error
  }
}

export async function handleEmployeesDeltaSync() {
  try {
    await sendPendingEmployeeUpdates()
    console.log('[MQ] [Sync] Employees delta sync completed')
  } catch (error) {
    console.error('[MQ] [Sync] Error in employees delta sync:', error)
    throw error
  }
}

export async function sendSyncStatus(type, status, details = {}) {
  return MqAdapter.withChannel(async channel => {
    try {
      await channel.assertQueue('sync-status', { durable: true })

      const message = {
        type,
        status,
        timestamp: new Date().toISOString(),
        details,
      }

      channel.sendToQueue('sync-status', Buffer.from(JSON.stringify(message)), { persistent: true })

      console.log(`[MQ] [Sync] Sync status sent: ${type} - ${status}`)
    } catch (error) {
      console.error('[MQ] [Sync] Error sending sync status:', error)
      throw error
    }
  })
}

export function startOutgoingPeriodicSync() {
  const interval = MqAdapter.config.syncInterval * 60 * 1000

  setInterval(async () => {
    try {
      await handleSalesDeltaSync()
      await handleRefundsDeltaSync()
      await handleEmployeesDeltaSync()

      await updateSyncStatus('outgoing', 'completed')
    } catch (error) {
      console.error('[MQ] [Sync] Error in outgoing periodic sync:', error)
      await updateSyncStatus('outgoing', 'failed', { error: error.message })
    }
  }, interval)

  console.log(
    `[MQ] [Sync] Outgoing periodic sync started with ${MqAdapter.config.syncInterval} minute intervals`
  )
}

async function updateSyncStatus(syncType, status, details = {}) {
  const db = getDatabase()
  const stmt = db.prepare(`
    INSERT OR REPLACE INTO sync_status (sync_type, last_sync, status, details)
    VALUES (?, ?, ?, ?)
  `)
  stmt.run(syncType, new Date().toISOString(), status, JSON.stringify(details))
}

export async function sendManualSync(categories = []) {
  try {
    if (categories.includes('sales') || categories.length === 0) {
      await handleSalesDeltaSync()
    }

    if (categories.includes('refunds') || categories.length === 0) {
      await handleRefundsDeltaSync()
    }

    if (categories.includes('employees') || categories.length === 0) {
      await handleEmployeesDeltaSync()
    }

    await sendSyncStatus('manual', 'completed', { categories })
    console.log('[MQ] [Sync] Manual sync completed')
  } catch (error) {
    await sendSyncStatus('manual', 'failed', { error: error.message, categories })
    console.error('[MQ] [Sync] Error in manual sync:', error)
    throw error
  }
}
