import { getDatabase } from '../../database.js'
import MqAdapter from '../adapter.js'

export async function sendSale(saleId) {
  return MqAdapter.withConfirmChannel(async channel => {
    try {
      await channel.assertQueue('sales', { durable: true })
      await channel.assertQueue('sales-response', { durable: true })

      const sale = await getSaleDetails(saleId)
      await sendSaleMessage(channel, sale)
      const response = await waitForSaleResponse(channel, saleId)

      if (response) {
        updateSaleTransmissionStatus(response.sale_id)
      }

      return response
    } catch (error) {
      console.error('[MQ] [Sales] Error sending sale:', error)
      throw error
    }
  })
}

async function getSaleDetails(saleId) {
  const db = getDatabase()

  const saleStmt = db.prepare(`
    SELECT * FROM sales WHERE id = ?
  `)
  const sale = saleStmt.get(saleId)

  if (!sale) {
    throw new Error(`Sale not found: ${saleId}`)
  }

  const itemsStmt = db.prepare(`
    SELECT * FROM sale_items WHERE sale_id = ?
  `)
  const items = itemsStmt.all(saleId)

  const paymentsStmt = db.prepare(`
    SELECT * FROM sale_payments WHERE sale_id = ?
  `)
  const payments = paymentsStmt.all(saleId)

  return {
    ...sale,
    items,
    payments,
  }
}

async function sendSaleMessage(channel, sale) {
  return new Promise((resolve, reject) => {
    channel.sendToQueue('sales', Buffer.from(JSON.stringify(sale)), { persistent: true }, err => {
      if (err) {
        reject(err)
      } else {
        resolve()
      }
    })
  })
}

async function waitForSaleResponse(channel, saleId, timeout = 120000) {
  return new Promise((resolve, reject) => {
    const timeoutTimer = setTimeout(() => {
      reject(new Error(`Sale sync timeout: ${saleId}`))
    }, timeout)

    channel.consume('sales-response', msg => {
      if (!msg) return

      try {
        const data = JSON.parse(msg.content.toString())

        if (data.success && data.sale_id === saleId) {
          clearTimeout(timeoutTimer)
          channel.ack(msg)
          resolve(data)
        } else {
          channel.ack(msg)
        }
      } catch (error) {
        console.error('[MQ] [Sales] Error processing sale response:', error)
        channel.nack(msg, false, false)
      }
    })
  })
}

function updateSaleTransmissionStatus(saleId) {
  const db = getDatabase()
  const stmt = db.prepare(`
    UPDATE sales SET
      transmitted_at = ?,
      transmission_status = 'completed'
    WHERE id = ?
  `)
  stmt.run(new Date().toISOString(), saleId)
  console.log(`[MQ] [Sales] Sale transmission status updated: ${saleId}`)
}

export async function sendPendingSales() {
  const db = getDatabase()
  const stmt = db.prepare(`
    SELECT id FROM sales
    WHERE transmission_status IS NULL OR transmission_status = 'pending'
    ORDER BY created_at ASC
    LIMIT 10
  `)

  const pendingSales = stmt.all()

  for (const sale of pendingSales) {
    try {
      await sendSale(sale.id)
      console.log(`[MQ] [Sales] Pending sale sent: ${sale.id}`)
    } catch (error) {
      console.error(`[MQ] [Sales] Error sending pending sale ${sale.id}:`, error)

      const updateStmt = db.prepare(`
        UPDATE sales SET
          transmission_status = 'failed',
          transmission_error = ?
        WHERE id = ?
      `)
      updateStmt.run(error.message, sale.id)
    }
  }
}
