import { getDatabase } from '../../database.js'
import MqAdapter from '../adapter.js'

export async function sendRefund(refundId) {
  return MqAdapter.withConfirmChannel(async channel => {
    try {
      await channel.assertQueue('refunds', { durable: true })
      await channel.assertQueue('refunds-response', { durable: true })

      const refund = await getRefundDetails(refundId)
      await sendRefundMessage(channel, refund)
      const response = await waitForRefundResponse(channel, refundId)

      if (response) {
        updateRefundTransmissionStatus(response.refund_id)
      }

      return response
    } catch (error) {
      console.error('[MQ] [Refunds] Error sending refund:', error)
      throw error
    }
  })
}

async function getRefundDetails(refundId) {
  const db = getDatabase()

  const refundStmt = db.prepare(`
    SELECT * FROM refunds WHERE id = ?
  `)
  const refund = refundStmt.get(refundId)

  if (!refund) {
    throw new Error(`Refund not found: ${refundId}`)
  }

  const itemsStmt = db.prepare(`
    SELECT * FROM refund_items WHERE refund_id = ?
  `)
  const items = itemsStmt.all(refundId)

  return {
    ...refund,
    items,
  }
}

async function sendRefundMessage(channel, refund) {
  return new Promise((resolve, reject) => {
    channel.sendToQueue(
      'refunds',
      Buffer.from(JSON.stringify(refund)),
      { persistent: true },
      err => {
        if (err) {
          reject(err)
        } else {
          resolve()
        }
      }
    )
  })
}

async function waitForRefundResponse(channel, refundId, timeout = 120000) {
  return new Promise((resolve, reject) => {
    const timeoutTimer = setTimeout(() => {
      reject(new Error(`Refund sync timeout: ${refundId}`))
    }, timeout)

    channel.consume('refunds-response', msg => {
      if (!msg) return

      try {
        const data = JSON.parse(msg.content.toString())

        if (data.success && data.refund_id === refundId) {
          clearTimeout(timeoutTimer)
          channel.ack(msg)
          resolve(data)
        } else {
          channel.ack(msg)
        }
      } catch (error) {
        console.error('[MQ] [Refunds] Error processing refund response:', error)
        channel.nack(msg, false, false)
      }
    })
  })
}

function updateRefundTransmissionStatus(refundId) {
  const db = getDatabase()
  const stmt = db.prepare(`
    UPDATE refunds SET
      transmitted_at = ?,
      transmission_status = 'completed'
    WHERE id = ?
  `)
  stmt.run(new Date().toISOString(), refundId)
  console.log(`[MQ] [Refunds] Refund transmission status updated: ${refundId}`)
}

export async function sendPendingRefunds() {
  const db = getDatabase()
  const stmt = db.prepare(`
    SELECT id FROM refunds
    WHERE transmission_status IS NULL OR transmission_status = 'pending'
    ORDER BY created_at ASC
    LIMIT 10
  `)

  const pendingRefunds = stmt.all()

  for (const refund of pendingRefunds) {
    try {
      await sendRefund(refund.id)
      console.log(`[MQ] [Refunds] Pending refund sent: ${refund.id}`)
    } catch (error) {
      console.error(`[MQ] [Refunds] Error sending pending refund ${refund.id}:`, error)

      const updateStmt = db.prepare(`
        UPDATE refunds SET
          transmission_status = 'failed',
          transmission_error = ?
        WHERE id = ?
      `)
      updateStmt.run(error.message, refund.id)
    }
  }
}
