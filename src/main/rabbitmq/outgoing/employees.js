import { getDatabase } from '../../database.js'
import MqAdapter from '../adapter.js'

export async function sendEmployeeUpdate(employeeData) {
  return MqAdapter.withConfirmChannel(async channel => {
    try {
      await channel.assertQueue('employee-updates', { durable: true })

      const message = {
        type: 'employee',
        action: employeeData.action || 'update',
        data: employeeData,
        timestamp: new Date().toISOString(),
      }

      await sendMessage(channel, 'employee-updates', message)
      console.log(`[MQ] [Employees] Employee update sent: ${employeeData.id}`)
    } catch (error) {
      console.error('[MQ] [Employees] Error sending employee update:', error)
      throw error
    }
  })
}

async function sendMessage(channel, queue, message) {
  return new Promise((resolve, reject) => {
    channel.sendToQueue(queue, Buffer.from(JSON.stringify(message)), { persistent: true }, err => {
      if (err) {
        reject(err)
      } else {
        resolve()
      }
    })
  })
}

export async function sendEmployeeAttendance(attendanceData) {
  return MqAdapter.withConfirmChannel(async channel => {
    try {
      await channel.assertQueue('employee-attendance', { durable: true })

      const message = {
        type: 'attendance',
        data: attendanceData,
        timestamp: new Date().toISOString(),
      }

      await sendMessage(channel, 'employee-attendance', message)
      console.log(`[MQ] [Employees] Attendance sent: ${attendanceData.employee_id}`)
    } catch (error) {
      console.error('[MQ] [Employees] Error sending attendance:', error)
      throw error
    }
  })
}

export async function sendPendingEmployeeUpdates() {
  const db = getDatabase()
  const stmt = db.prepare(`
    SELECT * FROM employee_updates_queue
    WHERE status = 'pending'
    ORDER BY created_at ASC
    LIMIT 10
  `)

  const pendingUpdates = stmt.all()

  for (const update of pendingUpdates) {
    try {
      await sendEmployeeUpdate(JSON.parse(update.data))

      const updateStmt = db.prepare(`
        UPDATE employee_updates_queue SET
          status = 'sent',
          sent_at = ?
        WHERE id = ?
      `)
      updateStmt.run(new Date().toISOString(), update.id)

      console.log(`[MQ] [Employees] Pending update sent: ${update.id}`)
    } catch (error) {
      console.error(`[MQ] [Employees] Error sending pending update ${update.id}:`, error)

      const updateStmt = db.prepare(`
        UPDATE employee_updates_queue SET
          status = 'failed',
          error_message = ?
        WHERE id = ?
      `)
      updateStmt.run(error.message, update.id)
    }
  }
}
