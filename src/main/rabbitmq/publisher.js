import MqAdapter from '../rabbitmq.js'

class Publisher {
  constructor() {
    this.publishCount = 0
    this.lastPublishTime = null
  }

  async publish(queueName, message) {
    const result = await MqAdapter.publishMessage(queueName, message)

    this.publishCount++
    this.lastPublishTime = new Date()

    return result
  }

  getStats() {
    return {
      publishCount: this.publishCount,
      lastPublishTime: this.lastPublishTime,
    }
  }

  resetStats() {
    this.publishCount = 0
    this.lastPublishTime = null
  }
}

export default Publisher
