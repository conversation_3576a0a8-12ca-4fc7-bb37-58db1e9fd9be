class ChannelPool {
  constructor(maxSize = 10) {
    this.maxSize = maxSize
    this.channels = new Map()
    this.idleChannels = []
    this.channelCounter = 0
    this.lastCleanup = Date.now()
    this.cleanupInterval = 30000
    this.idleTimeout = 180000 // 3 minutes
    this.periodicCleanupTimer = null
  }

  async acquireChannel(connection) {
    this.cleanup()

    // Return idle channel if available
    if (this.idleChannels.length > 0) {
      const channelInfo = this.idleChannels.pop()
      channelInfo.lastUsed = Date.now()
      channelInfo.inUse = true
      return channelInfo.channel
    }

    // Check pool limit
    if (this.channels.size >= this.maxSize) {
      throw new Error(
        `Channel pool limit exceeded. Max: ${this.maxSize}, Current: ${this.channels.size}`
      )
    }

    // Create new channel
    const channel = await connection.createChannel()
    const channelId = `channel-${++this.channelCounter}`

    // Set up channel event handlers
    channel.on('close', () => {
      this.handleChannelClose(channelId)
    })

    channel.on('error', error => {
      console.error(`Channel ${channelId} error:`, error)
      this.handleChannelError(channelId, error)
    })

    const channelInfo = {
      id: channelId,
      channel,
      created: Date.now(),
      lastUsed: Date.now(),
      inUse: true,
      errorCount: 0,
    }

    this.channels.set(channelId, channelInfo)
    return channel
  }

  releaseChannel(channel) {
    for (const [, channelInfo] of this.channels.entries()) {
      if (channelInfo.channel === channel) {
        channelInfo.inUse = false
        channelInfo.lastUsed = Date.now()
        this.idleChannels.push(channelInfo)
        return true
      }
    }
    return false
  }

  handleChannelClose(channelId) {
    if (this.channels.has(channelId)) {
      this.channels.delete(channelId)

      // Remove from idle channels if present
      const idleIndex = this.idleChannels.findIndex(info => info.id === channelId)
      if (idleIndex !== -1) {
        this.idleChannels.splice(idleIndex, 1)
      }
    }
  }

  handleChannelError(channelId, _error) {
    if (this.channels.has(channelId)) {
      const channelInfo = this.channels.get(channelId)
      channelInfo.errorCount = (channelInfo.errorCount || 0) + 1

      // Remove channels with too many errors
      if (channelInfo.errorCount > 3) {
        this.forceCloseChannel(channelId)
      }
    }
  }

  forceCloseChannel(channelId) {
    if (this.channels.has(channelId)) {
      const channelInfo = this.channels.get(channelId)
      try {
        channelInfo.channel.close().catch(() => {})
      } catch {
        // Ignore close errors
      }
      this.channels.delete(channelId)

      // Remove from idle channels
      const idleIndex = this.idleChannels.findIndex(info => info.id === channelId)
      if (idleIndex !== -1) {
        this.idleChannels.splice(idleIndex, 1)
      }
    }
  }

  cleanup() {
    const now = Date.now()
    if (now - this.lastCleanup < this.cleanupInterval) return

    const toRemove = []

    // Find expired channels
    for (const [id, channelInfo] of this.channels.entries()) {
      if (!channelInfo.inUse && now - channelInfo.lastUsed > this.idleTimeout) {
        toRemove.push(id)
      }
    }

    // Remove expired channels
    toRemove.forEach(id => {
      this.forceCloseChannel(id)
    })

    this.lastCleanup = now
  }

  startPeriodicCleanup(intervalMs = 60000) {
    if (this.periodicCleanupTimer) {
      return
    }

    this.periodicCleanupTimer = setInterval(() => {
      try {
        this.cleanup()
      } catch (error) {
        console.error('Error during periodic cleanup:', error)
      }
    }, intervalMs)
  }

  stopPeriodicCleanup() {
    if (this.periodicCleanupTimer) {
      clearInterval(this.periodicCleanupTimer)
      this.periodicCleanupTimer = null
    }
  }

  async closeAll() {
    this.stopPeriodicCleanup()

    const closePromises = []

    for (const channelInfo of this.channels.values()) {
      closePromises.push(
        channelInfo.channel.close().catch(error => {
          console.error('Error closing channel:', error)
        })
      )
    }

    try {
      await Promise.all(closePromises)
    } catch (error) {
      console.error('Error closing some channels:', error)
    }

    this.channels.clear()
    this.idleChannels = []
  }

  getStats() {
    const inUseCount = Array.from(this.channels.values()).filter(info => info.inUse).length
    const totalAge = Array.from(this.channels.values()).reduce((sum, info) => {
      return sum + (Date.now() - info.created)
    }, 0)
    const avgAge = this.channels.size > 0 ? totalAge / this.channels.size : 0

    return {
      total: this.channels.size,
      idle: this.idleChannels.length,
      inUse: inUseCount,
      maxSize: this.maxSize,
      utilization:
        this.maxSize > 0 ? `${((this.channels.size / this.maxSize) * 100).toFixed(1)}%` : '0%',
      averageAge: `${Math.round(avgAge / 1000)}s`,
      oldestChannel: this.getOldestChannelAge(),
      errorCount: this.getTotalErrorCount(),
    }
  }

  getOldestChannelAge() {
    if (this.channels.size === 0) return '0s'

    const oldest = Math.min(...Array.from(this.channels.values()).map(info => info.created))
    return `${Math.round((Date.now() - oldest) / 1000)}s`
  }

  getTotalErrorCount() {
    return Array.from(this.channels.values()).reduce((sum, info) => {
      return sum + (info.errorCount || 0)
    }, 0)
  }

  getDetailedStats() {
    return {
      ...this.getStats(),
      channels: Array.from(this.channels.entries()).map(([id, info]) => ({
        id,
        inUse: info.inUse,
        age: `${Math.round((Date.now() - info.created) / 1000)}s`,
        lastUsed: `${Math.round((Date.now() - info.lastUsed) / 1000)}s ago`,
        errorCount: info.errorCount || 0,
      })),
    }
  }
}

export default ChannelPool
