import MqAdapter from '../rabbitmq.js'

class Consumer {
  constructor() {
    this.activeConsumers = new Map()
  }

  async consume(queueName, callback) {
    await MqAdapter.consumeMessages(queueName, callback)

    this.activeConsumers.set(queueName, {
      queueName,
      callback,
    })
  }

  async stopConsumer(queueName) {
    if (this.activeConsumers.has(queueName)) {
      this.activeConsumers.delete(queueName)
    }
  }

  async stopAllConsumers() {
    this.activeConsumers.clear()
  }

  getActiveConsumers() {
    return Array.from(this.activeConsumers.keys())
  }
}

export default Consumer
