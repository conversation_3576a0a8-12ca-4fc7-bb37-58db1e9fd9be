/**
 * Test script to verify RabbitMQ adapter functionality
 * This can be run to test the setupConsumers function
 */

import MqAdapter from '../adapter.js'
import MqLogger from '../MqLogger.js'

const logger = MqLogger.getLogger('AdapterTest')

async function testAdapter() {
  try {
    logger.info('Starting adapter test...')

    // Test connection
    logger.info('Testing connection...')
    const connection = await MqAdapter.connect()
    logger.info('Connection successful:', !!connection)

    // Check if consumers are set up
    logger.info('Checking consumers...')
    const consumers = MqAdapter.getActiveConsumers()
    logger.info('Active consumers:', consumers)

    if (consumers.length === 0) {
      logger.warn('No consumers found, trying to set them up manually...')
      await MqAdapter.retrySetupConsumers()

      const retryConsumers = MqAdapter.getActiveConsumers()
      logger.info('Consumers after retry:', retryConsumers)
    }

    // Test channel pool
    if (MqAdapter.channelPool) {
      const stats = MqAdapter.channelPool.getStats()
      logger.info('Channel pool stats:', stats)
    }

    logger.info('Adapter test completed successfully')
  } catch (error) {
    logger.error('Adapter test failed:', error)
    throw error
  }
}

// Export for use in other modules
export { testAdapter }

// If running directly
if (import.meta.url === `file://${process.argv[1]}`) {
  testAdapter()
    .then(() => {
      console.log('✅ Test completed successfully')
      process.exit(0)
    })
    .catch(error => {
      console.error('❌ Test failed:', error)
      process.exit(1)
    })
}
