import { getDatabase } from '../../database.js'

class SyncMonitor {
  static startMonitoring() {
    setInterval(() => {
      this.checkSyncHealth()
    }, 60000) // Check every minute
  }

  static checkSyncHealth() {
    const db = getDatabase()

    try {
      const stmt = db.prepare(`
        SELECT sync_type, last_sync, status, details
        FROM sync_status
        WHERE last_sync > datetime('now', '-1 hour')
      `)
      const recentSyncs = stmt.all()

      for (const sync of recentSyncs) {
        const timeDiff = Date.now() - new Date(sync.last_sync).getTime()

        if (timeDiff > 30 * 60 * 1000 && sync.status === 'pending') {
          // 30 minutes
          console.warn(`[SyncMonitor] Sync ${sync.sync_type} has been pending for too long`)
          this.markSyncAsFailed(sync.sync_type, 'Timeout')
        }
      }
    } catch (error) {
      console.error('[SyncMonitor] Error checking sync health:', error)
    }
  }

  static markSyncAsFailed(syncType, reason) {
    const db = getDatabase()
    const stmt = db.prepare(`
      UPDATE sync_status
      SET status = 'failed',
          details = ?
      WHERE sync_type = ?
    `)
    stmt.run(JSON.stringify({ error: reason }), syncType)
  }

  static getSyncStats() {
    const db = getDatabase()

    try {
      const stmt = db.prepare(`
        SELECT
          sync_type,
          status,
          last_sync,
          details
        FROM sync_status
        ORDER BY last_sync DESC
      `)
      return stmt.all()
    } catch (error) {
      console.error('[SyncMonitor] Error getting sync stats:', error)
      return []
    }
  }

  static getFailedSyncs() {
    const db = getDatabase()

    try {
      const stmt = db.prepare(`
        SELECT * FROM sync_status
        WHERE status = 'failed'
        AND last_sync > datetime('now', '-24 hours')
        ORDER BY last_sync DESC
      `)
      return stmt.all()
    } catch (error) {
      console.error('[SyncMonitor] Error getting failed syncs:', error)
      return []
    }
  }
}

export default SyncMonitor
