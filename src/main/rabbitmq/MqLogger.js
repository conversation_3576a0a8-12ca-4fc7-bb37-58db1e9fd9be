const LOG_LEVELS = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
}

class MqLogger {
  static currentLevel = 'info'
  static options = {
    level: 'info',
    enableTimestamps: true,
    enableStackTraces: false,
  }

  constructor(context) {
    this.context = context
  }

  static configure(options) {
    if (typeof options === 'string') {
      MqLogger.currentLevel = options
      MqLogger.options.level = options
    } else {
      if (options.level) {
        MqLogger.currentLevel = options.level
        MqLogger.options.level = options.level
      }
      if (options.enableTimestamps !== undefined) {
        MqLogger.options.enableTimestamps = options.enableTimestamps
      }
      if (options.enableStackTraces !== undefined) {
        MqLogger.options.enableStackTraces = options.enableStackTraces
      }
    }
  }

  static setLogLevel(level) {
    if (LOG_LEVELS.hasOwnProperty(level)) {
      this.currentLevel = level
      this.options.level = level
    }
  }

  static getLogger(context) {
    return new MqLogger(context)
  }

  static createPrefix(level, context) {
    const timestamp = this.options.enableTimestamps ? new Date().toISOString() : ''

    const contextStr = context ? `[${context}]` : ''
    const levelStr = `[${level.toUpperCase()}]`

    return `${timestamp} ${levelStr}${contextStr}`.trim()
  }

  static formatForLog(obj) {
    if (obj === null || obj === undefined) {
      return obj
    }

    if (typeof obj === 'string' || typeof obj === 'number' || typeof obj === 'boolean') {
      return obj
    }

    try {
      return JSON.stringify(obj, null, 2)
    } catch (error) {
      return '[Circular or Non-serializable Object]'
    }
  }

  static cleanErrorForLog(error) {
    if (!(error instanceof Error)) {
      return error
    }

    const cleaned = {
      name: error.name,
      message: error.message,
    }

    if (this.options.enableStackTraces && error.stack) {
      cleaned.stack = error.stack
    }

    return cleaned
  }

  static debug(message, ...args) {
    if (LOG_LEVELS[this.currentLevel] <= LOG_LEVELS.debug) {
      const prefix = this.createPrefix('debug')
      const formattedArgs = args.map(arg =>
        arg instanceof Error
          ? this.cleanErrorForLog(arg)
          : typeof arg === 'object'
            ? this.formatForLog(arg)
            : arg
      )
      console.debug(`${prefix} ${message}`, ...formattedArgs)
    }
  }

  static info(message, ...args) {
    if (LOG_LEVELS[this.currentLevel] <= LOG_LEVELS.info) {
      const prefix = this.createPrefix('info')
      const formattedArgs = args.map(arg =>
        arg instanceof Error
          ? this.cleanErrorForLog(arg)
          : typeof arg === 'object'
            ? this.formatForLog(arg)
            : arg
      )
      console.log(`${prefix} ${message}`, ...formattedArgs)
    }
  }

  static warn(message, ...args) {
    if (LOG_LEVELS[this.currentLevel] <= LOG_LEVELS.warn) {
      const prefix = this.createPrefix('warn')
      const formattedArgs = args.map(arg =>
        arg instanceof Error
          ? this.cleanErrorForLog(arg)
          : typeof arg === 'object'
            ? this.formatForLog(arg)
            : arg
      )
      console.warn(`${prefix} ${message}`, ...formattedArgs)
    }
  }

  static error(message, ...args) {
    if (LOG_LEVELS[this.currentLevel] <= LOG_LEVELS.error) {
      const prefix = this.createPrefix('error')
      const formattedArgs = args.map(arg =>
        arg instanceof Error
          ? this.cleanErrorForLog(arg)
          : typeof arg === 'object'
            ? this.formatForLog(arg)
            : arg
      )
      console.error(`${prefix} ${message}`, ...formattedArgs)
    }
  }

  debug(message, ...args) {
    if (LOG_LEVELS[MqLogger.currentLevel] <= LOG_LEVELS.debug) {
      const prefix = MqLogger.createPrefix('debug', this.context)
      const formattedArgs = args.map(arg =>
        arg instanceof Error
          ? MqLogger.cleanErrorForLog(arg)
          : typeof arg === 'object'
            ? MqLogger.formatForLog(arg)
            : arg
      )
      console.debug(`${prefix} ${message}`, ...formattedArgs)
    }
  }

  info(message, ...args) {
    if (LOG_LEVELS[MqLogger.currentLevel] <= LOG_LEVELS.info) {
      const prefix = MqLogger.createPrefix('info', this.context)
      const formattedArgs = args.map(arg =>
        arg instanceof Error
          ? MqLogger.cleanErrorForLog(arg)
          : typeof arg === 'object'
            ? MqLogger.formatForLog(arg)
            : arg
      )
      console.log(`${prefix} ${message}`, ...formattedArgs)
    }
  }

  warn(message, ...args) {
    if (LOG_LEVELS[MqLogger.currentLevel] <= LOG_LEVELS.warn) {
      const prefix = MqLogger.createPrefix('warn', this.context)
      const formattedArgs = args.map(arg =>
        arg instanceof Error
          ? MqLogger.cleanErrorForLog(arg)
          : typeof arg === 'object'
            ? MqLogger.formatForLog(arg)
            : arg
      )
      console.warn(`${prefix} ${message}`, ...formattedArgs)
    }
  }

  error(message, ...args) {
    if (LOG_LEVELS[MqLogger.currentLevel] <= LOG_LEVELS.error) {
      const prefix = MqLogger.createPrefix('error', this.context)
      const formattedArgs = args.map(arg =>
        arg instanceof Error
          ? MqLogger.cleanErrorForLog(arg)
          : typeof arg === 'object'
            ? MqLogger.formatForLog(arg)
            : arg
      )
      console.error(`${prefix} ${message}`, ...formattedArgs)
    }
  }
}

export default MqLogger
