import { getDatabase } from '../../database.js'
import MqAdapter from '../adapter.js'

export async function requestInitialSync() {
  return MqAdapter.withChannel(async channel => {
    try {
      await channel.assertQueue('sync-request', { durable: true })
      await channel.assertQueue('sync-response', { durable: true })

      const syncRequest = {
        type: 'full_sync', // Changed from initial_sync to full_sync
        requested_at: new Date().toISOString(),
        categories: ['employees', 'promotions', 'fast_access', 'groups', 'price_updates'],
        force: true, // Force a complete sync
      }

      channel.sendToQueue('sync-request', Buffer.from(JSON.stringify(syncRequest)), {
        persistent: true,
      })

      console.log('[MQ] [Sync] Full sync requested for all categories')
    } catch (error) {
      console.error('[MQ] [Sync] Error requesting full sync:', error)
      throw error
    }
  })
}

export function startIncomingPeriodicSync() {
  const interval = MqAdapter.config.syncInterval * 60 * 1000

  setInterval(async () => {
    try {
      await requestPeriodicSync()
    } catch (error) {
      console.error('[MQ] [Sync] Error in periodic sync:', error)
    }
  }, interval)

  console.log(
    `[MQ] [Sync] Periodic sync started with ${MqAdapter.config.syncInterval} minute intervals`
  )
}

async function requestPeriodicSync() {
  return MqAdapter.withChannel(async channel => {
    try {
      await channel.assertQueue('sync-request', { durable: true })

      const db = getDatabase()
      const lastSyncStmt = db.prepare('SELECT last_sync FROM sync_status WHERE sync_type = ?')
      const lastSync = lastSyncStmt.get('incoming')

      const syncRequest = {
        type: 'delta_sync',
        requested_at: new Date().toISOString(),
        last_sync: lastSync?.last_sync || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        categories: ['employees', 'promotions', 'fast_access', 'groups', 'price_updates'],
      }

      channel.sendToQueue('sync-request', Buffer.from(JSON.stringify(syncRequest)), {
        persistent: true,
      })

      const updateSyncStmt = db.prepare(`
        INSERT OR REPLACE INTO sync_status (sync_type, last_sync, status)
        VALUES (?, ?, ?)
      `)
      updateSyncStmt.run('incoming', new Date().toISOString(), 'requested')

      console.log('[MQ] [Sync] Delta sync requested')
    } catch (error) {
      console.error('[MQ] [Sync] Error requesting delta sync:', error)
      throw error
    }
  })
}

export async function receiveSyncResponse(channel) {
  try {
    console.log('[MQ] [Sync] Starting sync response consumer setup...')

    await channel.assertQueue('sync-response', {
      durable: true,
    })

    const { consumerTag } = await channel.consume(
      'sync-response',
      async msg => {
        if (!msg) return

        try {
          const data = JSON.parse(msg.content.toString())
          console.log('[MQ] [Sync] Sync response received:', data.type, data.status)

          await processSyncResponse(data)
          channel.ack(msg)
        } catch (error) {
          console.error('[MQ] [Sync] Error processing sync response:', error)
          channel.nack(msg, false, false)
        }
      },
      { noAck: false }
    )

    console.log(`[MQ] [Sync] Sync response consumer started with tag: ${consumerTag}`)
    return consumerTag
  } catch (error) {
    console.error('[MQ] [Sync] Error setting up sync response consumer:', error)
    throw error
  }
}

async function processSyncResponse(data) {
  const db = getDatabase()

  if (data.status === 'completed') {
    const updateSyncStmt = db.prepare(`
      INSERT OR REPLACE INTO sync_status (sync_type, last_sync, status, details)
      VALUES (?, ?, ?, ?)
    `)
    updateSyncStmt.run(
      'incoming',
      data.completed_at,
      'completed',
      JSON.stringify({
        type: data.type,
        records_processed: data.records_processed || 0,
        categories: data.categories || [],
      })
    )

    console.log(
      `[MQ] [Sync] Sync completed: ${data.type}, ${data.records_processed || 0} records processed`
    )
  } else if (data.status === 'failed') {
    const updateSyncStmt = db.prepare(`
      INSERT OR REPLACE INTO sync_status (sync_type, last_sync, status, details)
      VALUES (?, ?, ?, ?)
    `)
    updateSyncStmt.run(
      'incoming',
      data.failed_at,
      'failed',
      JSON.stringify({
        type: data.type,
        error: data.error || 'Unknown error',
      })
    )

    console.error(`[MQ] [Sync] Sync failed: ${data.type}, Error: ${data.error}`)
  }
}

export async function requestEmployeeFullSync() {
  return MqAdapter.withChannel(async channel => {
    try {
      await channel.assertQueue('sync-request', { durable: true })

      // Clear last sync for employees to force full sync
      const db = getDatabase()
      const clearSyncStmt = db.prepare('DELETE FROM sync_status WHERE sync_type = ?')
      clearSyncStmt.run('employees')

      const syncRequest = {
        type: 'full_sync',
        requested_at: new Date().toISOString(),
        categories: ['employees'],
        force: true,
        clear_cache: true, // Request to ignore any cached data
        reset_sync: true, // Reset sync timestamps
      }

      channel.sendToQueue('sync-request', Buffer.from(JSON.stringify(syncRequest)), {
        persistent: true,
      })

      console.log('[MQ] [Sync] Employee full sync requested with force flags')

      return { success: true, message: 'Employee full sync requested' }
    } catch (error) {
      console.error('[MQ] [Sync] Error requesting employee full sync:', error)
      throw error
    }
  })
}
