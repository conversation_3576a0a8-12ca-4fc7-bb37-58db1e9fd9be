export async function receiveConnectionTest(channel) {
  try {
    console.log('[MQ] [Test] Starting connection test consumer setup...')

    await channel.assertQueue('client-connection-test', {
      durable: false,
    })
    console.log('[MQ] [Test] Connection test queue asserted successfully')

    const { consumerTag } = await channel.consume(
      'client-connection-test',
      msg => {
        if (!msg) {
          console.log('[MQ] [Test] Received null message')
          return
        }

        try {
          console.log('[MQ] [Test] Raw message received:', {
            messageSize: msg.content.length,
            routingKey: msg.fields.routingKey,
            exchange: msg.fields.exchange,
          })

          const data = JSON.parse(msg.content.toString())

          console.log('[MQ] [Test] Connection test message:', data)

          // Acknowledge message
          channel.ack(msg)
          console.log('[MQ] [Test] Connection test message acknowledged successfully')
        } catch (error) {
          console.error('[MQ] [Test] Error parsing message:', error)
          channel.nack(msg, false, false)
        }
      },
      {
        noAck: false,
      }
    )

    console.log(`[MQ] [Test] Connection test consumer started with tag: ${consumerTag}`)
    return consumerTag
  } catch (error) {
    console.error('[MQ] [Test] Error setting up connection test consumer:', error)
    throw error
  }
}
