import { getDatabase } from '../../database.js'

export async function receiveDisplayChanges(channel) {
  try {
    console.log('[MQ] [DisplayChanges] Starting display changes consumer setup...')

    await channel.assertQueue('display-changes', {
      durable: true,
    })

    const { consumerTag } = await channel.consume(
      'display-changes',
      async msg => {
        if (!msg) return

        try {
          const data = JSON.parse(msg.content.toString())
          console.log('[MQ] [DisplayChanges] Message received:', data.action, data.data?.id)

          await processDisplayChangeMessage(data)
          channel.ack(msg)
        } catch (error) {
          console.error('[MQ] [DisplayChanges] Error processing message:', error)
          channel.nack(msg, false, false)
        }
      },
      { noAck: false }
    )

    console.log(`[MQ] [DisplayChanges] Consumer started with tag: ${consumerTag}`)
    return consumerTag
  } catch (error) {
    console.error('[MQ] [DisplayChanges] Error setting up consumer:', error)
    throw error
  }
}

async function processDisplayChangeMessage(data) {
  const db = getDatabase()

  switch (data.action) {
    case 'create':
    case 'update': {
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO display_changes (
          id, element_type, element_id, change_type, new_value,
          old_value, applied_at, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `)
      stmt.run(
        data.data.id,
        data.data.element_type,
        data.data.element_id,
        data.data.change_type,
        JSON.stringify(data.data.new_value),
        JSON.stringify(data.data.old_value),
        data.data.applied_at,
        data.data.created_at
      )
      console.log(`[MQ] [DisplayChanges] Display change ${data.action}d:`, data.data.id)
      break
    }
    case 'delete': {
      const deleteStmt = db.prepare('DELETE FROM display_changes WHERE id = ?')
      deleteStmt.run(data.data.id)
      console.log('[MQ] [DisplayChanges] Display change deleted:', data.data.id)
      break
    }
  }
}
