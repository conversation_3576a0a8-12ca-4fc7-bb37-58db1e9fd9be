import { getDatabase } from '../../database.js'

export async function receiveEmployees(channel) {
  try {
    console.log('[MQ] [Employees] Starting employee consumer setup...')

    await channel.assertQueue('employees', {
      durable: true,
    })
    console.log('[MQ] [Employees] employees queue asserted successfully')

    const { consumerTag } = await channel.consume(
      'employees',
      msg => {
        if (!msg) {
          console.log('[MQ] [Employees] Received null message')
          return
        }

        try {
          console.log('[MQ] [Employees] Raw message received:', {
            messageSize: msg.content.length,
            routingKey: msg.fields.routingKey,
            exchange: msg.fields.exchange,
          })

          const data = JSON.parse(msg.content.toString())

          console.log('[MQ] [Employees] Parsed message:', {
            type: data.type,
            action: data.action,
            hasData: !!data.data,
          })

          processEmployeeMessage(data)
            .then(() => {
              channel.ack(msg)
              console.log('[MQ] [Employees] Message acknowledged successfully')
            })
            .catch(error => {
              console.error('[MQ] [Employees] Error processing message:', error)
              channel.nack(msg, false, false)
            })
        } catch (error) {
          console.error('[MQ] [Employees] Error parsing message:', error)
          channel.nack(msg, false, false)
        }
      },
      {
        noAck: false,
      }
    )

    console.log(`[MQ] [Employees] Consumer started with tag: ${consumerTag}`)
    return consumerTag
  } catch (error) {
    console.error('[MQ] [Employees] Error setting up consumer:', error)
    throw error
  }
}

async function processEmployeeMessage(data) {
  const db = getDatabase()

  switch (data.type) {
    case 'employee':
      await processEmployee(db, data.action, data.data)
      break
    case 'role':
      await processRole(db, data.action, data.data)
      break
    case 'permission':
      await processPermission(db, data.action, data.data)
      break
    default:
      console.warn('[MQ] [Employees] Unknown message type:', data.type)
  }
}

async function processEmployee(db, action, data) {
  switch (action) {
    case 'create':
    case 'update': {
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO employees (
          id, name, email, phone, position, department,
          hire_date, is_active, role_id, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)
      stmt.run(
        data.employee.id,
        data.employee.name,
        data.employee.email,
        data.employee.phone,
        data.employee.position,
        data.employee.department,
        data.employee.hire_date,
        data.employee.is_active ? 1 : 0,
        data.employee.role_id,
        data.employee.created_at,
        data.employee.updated_at || new Date().toISOString()
      )
      console.log(`[MQ] [Employees] Employee ${action}d:`, data.employee.id)
      break
    }
    case 'delete': {
      const deleteStmt = db.prepare('DELETE FROM employees WHERE id = ?')
      deleteStmt.run(data.employee.id)
      console.log(`[MQ] [Employees] Employee deleted:`, data.employee.id)
      break
    }
  }
}

async function processRole(db, action, data) {
  switch (action) {
    case 'create':
    case 'update': {
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO roles (
          id, name, description, permissions, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?)
      `)
      stmt.run(
        data.id,
        data.name,
        data.description,
        JSON.stringify(data.permissions),
        data.created_at,
        data.updated_at || new Date().toISOString()
      )
      console.log(`[MQ] [Employees] Role ${action}d:`, data.id)
      break
    }
    case 'delete': {
      const deleteStmt = db.prepare('DELETE FROM roles WHERE id = ?')
      deleteStmt.run(data.id)
      console.log(`[MQ] [Employees] Role deleted:`, data.id)
      break
    }
  }
}

async function processPermission(db, action, data) {
  switch (action) {
    case 'create':
    case 'update': {
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO permissions (
          id, name, description, module, action_type, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `)
      stmt.run(
        data.id,
        data.name,
        data.description,
        data.module,
        data.action_type,
        data.created_at,
        data.updated_at || new Date().toISOString()
      )
      console.log(`[MQ] [Employees] Permission ${action}d:`, data.id)
      break
    }
    case 'delete': {
      const deleteStmt = db.prepare('DELETE FROM permissions WHERE id = ?')
      deleteStmt.run(data.id)
      console.log(`[MQ] [Employees] Permission deleted:`, data.id)
      break
    }
  }
}
