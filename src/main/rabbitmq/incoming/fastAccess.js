import { getDatabase } from '../../database.js'

export async function receiveFastAccess(channel) {
  try {
    console.log('[MQ] [FastAccess] Starting fast access consumer setup...')

    await channel.assertQueue('fast-access', {
      durable: true,
    })

    const { consumerTag } = await channel.consume(
      'fast-access',
      async msg => {
        if (!msg) return

        try {
          const data = JSON.parse(msg.content.toString())
          console.log('[MQ] [FastAccess] Message received:', data.action, data.data?.id)

          await processFastAccessMessage(data)
          channel.ack(msg)
        } catch (error) {
          console.error('[MQ] [FastAccess] Error processing message:', error)
          channel.nack(msg, false, false)
        }
      },
      { noAck: false }
    )

    console.log(`[MQ] [FastAccess] Consumer started with tag: ${consumerTag}`)
    return consumerTag
  } catch (error) {
    console.error('[MQ] [FastAccess] Error setting up consumer:', error)
    throw error
  }
}

async function processFastAccessMessage(data) {
  const db = getDatabase()

  switch (data.action) {
    case 'create':
    case 'update': {
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO fast_access_items (
          id, product_id, position, is_active, category_id,
          created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `)
      stmt.run(
        data.data.id,
        data.data.product_id,
        data.data.position,
        data.data.is_active ? 1 : 0,
        data.data.category_id,
        data.data.created_at,
        data.data.updated_at || new Date().toISOString()
      )
      console.log(`[MQ] [FastAccess] Fast access item ${data.action}d:`, data.data.id)
      break
    }
    case 'delete': {
      const deleteStmt = db.prepare('DELETE FROM fast_access_items WHERE id = ?')
      deleteStmt.run(data.data.id)
      console.log('[MQ] [FastAccess] Fast access item deleted:', data.data.id)
      break
    }
  }
}
