import { getDatabase } from '../../database.js'

export async function receiveGroups(channel) {
  try {
    console.log('[MQ] [Groups] Starting groups consumer setup...')

    await channel.assertQueue('groups', {
      durable: true,
    })

    const { consumerTag } = await channel.consume(
      'groups',
      async msg => {
        if (!msg) return

        try {
          const data = JSON.parse(msg.content.toString())
          console.log('[MQ] [Groups] Message received:', data.action, data.data?.id)

          await processGroupMessage(data)
          channel.ack(msg)
        } catch (error) {
          console.error('[MQ] [Groups] Error processing message:', error)
          channel.nack(msg, false, false)
        }
      },
      { noAck: false }
    )

    console.log(`[MQ] [Groups] Consumer started with tag: ${consumerTag}`)
    return consumerTag
  } catch (error) {
    console.error('[MQ] [Groups] Error setting up consumer:', error)
    throw error
  }
}

async function processGroupMessage(data) {
  const db = getDatabase()

  switch (data.action) {
    case 'create':
    case 'update': {
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO product_groups (
          id, name, description, parent_id, sort_order,
          is_active, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `)
      stmt.run(
        data.data.id,
        data.data.name,
        data.data.description,
        data.data.parent_id,
        data.data.sort_order,
        data.data.is_active ? 1 : 0,
        data.data.created_at,
        data.data.updated_at || new Date().toISOString()
      )
      console.log(`[MQ] [Groups] Group ${data.action}d:`, data.data.id)
      break
    }
    case 'delete': {
      const deleteStmt = db.prepare('DELETE FROM product_groups WHERE id = ?')
      deleteStmt.run(data.data.id)
      console.log('[MQ] [Groups] Group deleted:', data.data.id)
      break
    }
  }
}
