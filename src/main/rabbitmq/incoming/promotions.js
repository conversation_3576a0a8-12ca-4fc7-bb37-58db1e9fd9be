import { getDatabase } from '../../database.js'

export async function receivePromotions(channel) {
  try {
    console.log('[MQ] [Promotions] Starting promotions consumer setup...')

    await channel.assertQueue('promotions', {
      durable: true,
    })

    const { consumerTag } = await channel.consume(
      'promotions',
      async msg => {
        if (!msg) return

        try {
          const data = JSON.parse(msg.content.toString())
          console.log('[MQ] [Promotions] Message received:', data.action, data.data?.id)

          await processPromotionMessage(data)
          channel.ack(msg)
        } catch (error) {
          console.error('[MQ] [Promotions] Error processing message:', error)
          channel.nack(msg, false, false)
        }
      },
      { noAck: false }
    )

    console.log(`[MQ] [Promotions] Consumer started with tag: ${consumerTag}`)
    return consumerTag
  } catch (error) {
    console.error('[MQ] [Promotions] Error setting up consumer:', error)
    throw error
  }
}

async function processPromotionMessage(data) {
  const db = getDatabase()

  switch (data.action) {
    case 'create':
    case 'update': {
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO promotions (
          id, name, description, discount_type, discount_value,
          start_date, end_date, is_active, conditions, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)
      stmt.run(
        data.data.id,
        data.data.name,
        data.data.description,
        data.data.discount_type,
        data.data.discount_value,
        data.data.start_date,
        data.data.end_date,
        data.data.is_active ? 1 : 0,
        JSON.stringify(data.data.conditions || {}),
        data.data.created_at,
        data.data.updated_at || new Date().toISOString()
      )
      console.log(`[MQ] [Promotions] Promotion ${data.action}d:`, data.data.id)
      break
    }
    case 'delete': {
      const deleteStmt = db.prepare('DELETE FROM promotions WHERE id = ?')
      deleteStmt.run(data.data.id)
      console.log('[MQ] [Promotions] Promotion deleted:', data.data.id)
      break
    }
  }
}
