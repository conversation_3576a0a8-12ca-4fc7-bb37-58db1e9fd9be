import { getDatabase } from '../../database.js'

export async function receivePriceUpdates(channel) {
  try {
    console.log('[MQ] [PriceUpdates] Starting price updates consumer setup...')

    await channel.assertQueue('price-updates', {
      durable: true,
    })

    const { consumerTag } = await channel.consume(
      'price-updates',
      async msg => {
        if (!msg) return

        try {
          const data = JSON.parse(msg.content.toString())
          console.log('[MQ] [PriceUpdates] Message received:', data.action, data.data?.product_id)

          await processPriceUpdateMessage(data)
          channel.ack(msg)
        } catch (error) {
          console.error('[MQ] [PriceUpdates] Error processing message:', error)
          channel.nack(msg, false, false)
        }
      },
      { noAck: false }
    )

    console.log(`[MQ] [PriceUpdates] Consumer started with tag: ${consumerTag}`)
    return consumerTag
  } catch (error) {
    console.error('[MQ] [PriceUpdates] Error setting up consumer:', error)
    throw error
  }
}

async function processPriceUpdateMessage(data) {
  const db = getDatabase()

  switch (data.action) {
    case 'update': {
      const updateProductStmt = db.prepare(`
        UPDATE products SET
          price = ?,
          updated_at = ?
        WHERE id = ?
      `)
      updateProductStmt.run(data.data.new_price, new Date().toISOString(), data.data.product_id)

      const insertHistoryStmt = db.prepare(`
        INSERT INTO price_history (
          product_id, old_price, new_price, changed_at, changed_by
        ) VALUES (?, ?, ?, ?, ?)
      `)
      insertHistoryStmt.run(
        data.data.product_id,
        data.data.old_price,
        data.data.new_price,
        data.data.changed_at || new Date().toISOString(),
        data.data.changed_by || 'system'
      )

      console.log(`[MQ] [PriceUpdates] Price updated for product:`, data.data.product_id)
      break
    }
    case 'bulk_update': {
      const transaction = db.transaction(updates => {
        const updateStmt = db.prepare('UPDATE products SET price = ?, updated_at = ? WHERE id = ?')
        const historyStmt = db.prepare(`
          INSERT INTO price_history (product_id, old_price, new_price, changed_at, changed_by)
          VALUES (?, ?, ?, ?, ?)
        `)

        for (const update of updates) {
          updateStmt.run(update.new_price, new Date().toISOString(), update.product_id)
          historyStmt.run(
            update.product_id,
            update.old_price,
            update.new_price,
            update.changed_at || new Date().toISOString(),
            update.changed_by || 'system'
          )
        }
      })

      transaction(data.data.updates)
      console.log(
        `[MQ] [PriceUpdates] Bulk price update completed for ${data.data.updates.length} products`
      )
      break
    }
  }
}
