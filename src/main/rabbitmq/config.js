const config = {
  development: {
    url: process.env.VITE_MQ_URL || 'amqp://guest:guest@localhost:5672',
    maxRetries: Number(process.env.MQ_MAX_RETRIES) || 5,
    retryDelay: Number(process.env.MQ_RETRY_DELAY) || 2500,
    poolSize: Number(process.env.MQ_POOL_SIZE) || 15,
    channelIdleTimeoutMs: Number(process.env.MQ_CHANNEL_IDLE_TIMEOUT) || 180000,
    channelCleanupIntervalMs: Number(process.env.MQ_CHANNEL_CLEANUP_INTERVAL) || 30000,
    maxChannelsPerConnection: Number(process.env.MQ_MAX_CHANNELS_PER_CONNECTION) || 10,
    prefetchCount: Number(process.env.MQ_PREFETCH_COUNT) || 1,
    heartbeatInterval: Number(process.env.MQ_HEARTBEAT_INTERVAL) || 60,
    connectionTimeout: Number(process.env.MQ_CONNECTION_TIMEOUT) || 30000,
    channelRecoveryDelay: Number(process.env.MQ_CHANNEL_RECOVERY_DELAY) || 2000,
    logLevel: process.env.MQ_LOG_LEVEL || 'info',
    syncInterval: Number(process.env.MQ_SYNC_INTERVAL) || 5,
    messageAckTimeoutMs: Number(process.env.MQ_MESSAGE_ACK_TIMEOUT) || 30000,
    consumerCancelTimeoutMs: Number(process.env.MQ_CONSUMER_CANCEL_TIMEOUT) || 10000,
  },
  production: {
    url: process.env.VITE_MQ_URL || 'amqp://guest:guest@localhost:5672',
    maxRetries: Number(process.env.MQ_MAX_RETRIES) || 10,
    retryDelay: Number(process.env.MQ_RETRY_DELAY) || 5000,
    poolSize: Number(process.env.MQ_POOL_SIZE) || 10,
    channelIdleTimeoutMs: Number(process.env.MQ_CHANNEL_IDLE_TIMEOUT) || 300000,
    channelCleanupIntervalMs: Number(process.env.MQ_CHANNEL_CLEANUP_INTERVAL) || 60000,
    maxChannelsPerConnection: Number(process.env.MQ_MAX_CHANNELS_PER_CONNECTION) || 20,
    prefetchCount: Number(process.env.MQ_PREFETCH_COUNT) || 1,
    heartbeatInterval: Number(process.env.MQ_HEARTBEAT_INTERVAL) || 60,
    connectionTimeout: Number(process.env.MQ_CONNECTION_TIMEOUT) || 30000,
    channelRecoveryDelay: Number(process.env.MQ_CHANNEL_RECOVERY_DELAY) || 2000,
    logLevel: process.env.MQ_LOG_LEVEL || 'warn',
    syncInterval: Number(process.env.MQ_SYNC_INTERVAL) || 10,
    messageAckTimeoutMs: Number(process.env.MQ_MESSAGE_ACK_TIMEOUT) || 30000,
    consumerCancelTimeoutMs: Number(process.env.MQ_CONSUMER_CANCEL_TIMEOUT) || 10000,
  },
}

class MqConfig {
  static getInstance() {
    const env = process.env.NODE_ENV || 'development'
    return config[env] || config.development
  }

  static isValidConfig(config) {
    const required = ['url', 'maxRetries', 'retryDelay', 'poolSize']
    return required.every(key => config[key] !== undefined)
  }

  static getQueueConfig(queueName) {
    const baseConfig = {
      durable: true,
      autoDelete: false,
      exclusive: false,
    }

    const queueConfigs = {
      sales: { ...baseConfig, messageTtl: 3600000 },
      'sales-response': { ...baseConfig, messageTtl: 1800000 },
      refunds: { ...baseConfig, messageTtl: 3600000 },
      'refunds-response': { ...baseConfig, messageTtl: 1800000 },
      'local-employees': { ...baseConfig },
      'local-promotions': { ...baseConfig },
      'local-fast-access': { ...baseConfig },
      'local-groups': { ...baseConfig },
      'local-display-changes': { ...baseConfig },
      'local-price-updates': { ...baseConfig },
      'sync-request': { ...baseConfig, messageTtl: 600000 },
      'sync-response': { ...baseConfig, messageTtl: 600000 },
    }

    return queueConfigs[queueName] || baseConfig
  }
}

export default MqConfig
