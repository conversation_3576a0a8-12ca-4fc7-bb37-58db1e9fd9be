import amqp from 'amqplib'
import { EventEmitter } from 'events'
import ChannelPool from './channelPool.js'
import MqConfig from './config.js'
import MqLogger from './MqLogger.js'

import { receiveConnectionTest } from './incoming/connectionTest.js'
import { receiveDisplayChanges } from './incoming/displayChanges.js'
import { receiveEmployees } from './incoming/employees.js'
import { receiveFastAccess } from './incoming/fastAccess.js'
import { receiveGroups } from './incoming/groups.js'
import { receiveLocalEmployees } from './incoming/localEmployees.js'
import { receivePriceUpdates } from './incoming/priceUpdates.js'
import { receivePromotions } from './incoming/promotions.js'
import {
  receiveSyncResponse,
  requestInitialSync,
  startIncomingPeriodicSync,
} from './incoming/sync.js'
import SyncMonitor from './utils/syncMonitor.js'

const logger = MqLogger.getLogger('MqAdapter')

const CONSUMER_TYPES = {
  EMPLOYEES: 'employees',
  LOCAL_EMPLOYEES: 'local-employees',
  CONNECTION_TEST: 'connection-test',
  FAST_ACCESS: 'fast-access',
  PROMOTIONS: 'promotions',
  GROUPS: 'groups',
  DISPLAY_CHANGES: 'display-changes',
  SALES_SYNC: 'sales-sync',
  REFUNDS_SYNC: 'refunds-sync',
  PRICE_UPDATES_SYNC: 'price-updates-sync',
  SYNC_RESPONSE: 'sync-response',
}

class MqAdapter extends EventEmitter {
  static connection = null
  static instance = null
  static isConnecting = false
  static isInitialConnection = true
  static isShuttingDown = false
  static channelPool = null
  static config = MqConfig.getInstance()
  static activeConsumers = new Map()
  static reconnectTimer = null
  static reconnectAttempts = 0

  static getInstance() {
    if (!this.instance) {
      this.instance = new MqAdapter()
    }
    return this.instance
  }

  static async connect() {
    if (this.connection && !this.connection.connection.destroyed) {
      return this.connection
    }

    if (this.isConnecting) {
      return new Promise((resolve, reject) => {
        const checkConnection = setInterval(() => {
          if (this.connection && !this.isConnecting) {
            clearInterval(checkConnection)
            resolve(this.connection)
          } else if (!this.isConnecting) {
            clearInterval(checkConnection)
            reject(new Error('Connection failed'))
          }
        }, 100)
      })
    }

    this.isConnecting = true

    try {
      logger.info('Connecting to RabbitMQ...')
      this.connection = await amqp.connect(this.config.url, {
        heartbeat: this.config.heartbeatInterval,
      })

      this.channelPool = new ChannelPool(this.config.poolSize)

      // Start periodic cleanup for the channel pool
      this.channelPool.startPeriodicCleanup()

      this.connection.on('close', () => {
        logger.warn('Connection closed, attempting to reconnect...')
        this.handleConnectionLoss()
      })

      this.connection.on('error', error => {
        logger.error('Connection error:', error)
        this.handleConnectionLoss()
      })

      logger.info('Connected to RabbitMQ successfully')
      this.isConnecting = false
      this.reconnectAttempts = 0

      if (this.isInitialConnection) {
        logger.info('Initial connection - setting up consumers...')
        try {
          await this.setupConsumers()
          logger.info('Consumers setup completed on initial connection')
        } catch (error) {
          logger.error('Failed to setup consumers on initial connection:', error)
          // Don't throw here to allow connection to succeed, consumers can be retried
        }
        this.isInitialConnection = false
      }

      return this.connection
    } catch (error) {
      this.isConnecting = false
      logger.error('Failed to connect to RabbitMQ:', error)
      throw error
    }
  }

  static async setupConsumers() {
    try {
      logger.info('Setting up consumers...')

      // Ensure we have a valid connection and channel pool
      if (!this.connection || this.connection.connection.destroyed) {
        throw new Error('No valid connection available for setting up consumers')
      }

      if (!this.channelPool) {
        throw new Error('Channel pool not initialized')
      }

      logger.info('Starting individual consumers...')

      await this.startConsumer(CONSUMER_TYPES.EMPLOYEES, receiveEmployees)
      logger.info('Employees consumer started')

      // Start local employees consumer for API integration
      await this.startConsumer(CONSUMER_TYPES.LOCAL_EMPLOYEES, receiveLocalEmployees)
      logger.info('Local employees consumer started')

      // Start connection test consumer
      await this.startConsumer(CONSUMER_TYPES.CONNECTION_TEST, receiveConnectionTest)
      logger.info('Connection test consumer started')

      await this.startConsumer(CONSUMER_TYPES.PROMOTIONS, receivePromotions)
      logger.info('Promotions consumer started')

      await this.startConsumer(CONSUMER_TYPES.FAST_ACCESS, receiveFastAccess)
      logger.info('Fast access consumer started')

      await this.startConsumer(CONSUMER_TYPES.GROUPS, receiveGroups)
      logger.info('Groups consumer started')

      await this.startConsumer(CONSUMER_TYPES.DISPLAY_CHANGES, receiveDisplayChanges)
      logger.info('Display changes consumer started')

      await this.startConsumer(CONSUMER_TYPES.PRICE_UPDATES_SYNC, receivePriceUpdates)
      logger.info('Price updates consumer started')

      await this.startConsumer(CONSUMER_TYPES.SYNC_RESPONSE, receiveSyncResponse)
      logger.info('Sync response consumer started')

      // New consumer setup
      await this.startConsumer(CONSUMER_TYPES.LOCAL_EMPLOYEES, receiveLocalEmployees)
      logger.info('Local employees consumer started')

      logger.info('Starting sync operations...')
      await requestInitialSync()
      startIncomingPeriodicSync()
      SyncMonitor.startMonitoring()

      logger.info('All consumers set up successfully')
    } catch (error) {
      logger.error('Error setting up consumers:', error)
      throw error
    }
  }

  static async startConsumer(type, consumerFunction) {
    try {
      if (this.activeConsumers.has(type)) {
        logger.warn(`Consumer ${type} already active`)
        return
      }

      logger.info(`Starting consumer: ${type}`)

      if (!this.connection || this.connection.connection.destroyed) {
        throw new Error(`No valid connection for consumer ${type}`)
      }

      const channel = await this.channelPool.acquireChannel(this.connection)

      if (!channel) {
        throw new Error(`Failed to acquire channel for consumer ${type}`)
      }

      await channel.prefetch(this.config.prefetchCount)
      logger.info(`Set prefetch count to ${this.config.prefetchCount} for consumer ${type}`)

      await consumerFunction(channel)

      this.activeConsumers.set(type, {
        channel,
        started: Date.now(),
        type,
      })

      logger.info(`Consumer ${type} started successfully`)
    } catch (error) {
      logger.error(`Error starting consumer ${type}:`, error)
      throw error
    }
  }

  static async withChannel(callback) {
    const connection = await this.connect()
    const channel = await this.channelPool.acquireChannel(connection)

    try {
      return await callback(channel)
    } finally {
      this.channelPool.releaseChannel(channel)
    }
  }

  static async withConfirmChannel(callback) {
    const connection = await this.connect()
    const channel = await connection.createConfirmChannel()

    try {
      return await callback(channel)
    } finally {
      await channel.close()
    }
  }

  static handleConnectionLoss() {
    if (this.isShuttingDown) return

    this.connection = null
    this.activeConsumers.clear()

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    const delay = Math.min(this.config.retryDelay * Math.pow(2, this.reconnectAttempts), 30000)
    this.reconnectAttempts++

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connect()
        await this.setupConsumers()
      } catch (error) {
        logger.error('Reconnection failed:', error)
        this.handleConnectionLoss()
      }
    }, delay)
  }

  static async shutdown() {
    this.isShuttingDown = true

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
    }

    if (this.channelPool) {
      await this.channelPool.closeAll()
    }

    if (this.connection) {
      await this.connection.close()
    }

    this.activeConsumers.clear()
    logger.info('MqAdapter shutdown complete')
  }

  static async retrySetupConsumers() {
    try {
      logger.info('Retrying consumer setup...')

      // Clear existing consumers
      this.activeConsumers.clear()

      await this.setupConsumers()
      logger.info('Consumer setup retry completed successfully')
    } catch (error) {
      logger.error('Consumer setup retry failed:', error)
      throw error
    }
  }

  static getActiveConsumers() {
    const consumers = []
    for (const [type, info] of this.activeConsumers.entries()) {
      consumers.push({
        type,
        started: new Date(info.started).toISOString(),
        uptime: Date.now() - info.started,
      })
    }
    return consumers
  }

  static async testConsumerSetup() {
    try {
      logger.info('Testing consumer setup...')

      // Check connection first
      if (!this.connection || this.connection.connection.destroyed) {
        logger.info('No connection, establishing connection first...')
        await this.connect()
      }

      // Clear existing consumers for fresh test
      logger.info('Clearing existing consumers...')
      this.activeConsumers.clear()

      // Try to setup consumers
      logger.info('Attempting to setup consumers...')
      await this.setupConsumers()

      // Report results
      const activeConsumers = this.getActiveConsumers()
      logger.info(`Consumer setup test completed. ${activeConsumers.length} consumers active:`)
      activeConsumers.forEach(consumer => {
        logger.info(`- ${consumer.type}: running for ${Math.round(consumer.uptime / 1000)}s`)
      })

      return {
        success: true,
        consumersCount: activeConsumers.length,
        consumers: activeConsumers,
      }
    } catch (error) {
      logger.error('Consumer setup test failed:', error)
      return {
        success: false,
        error: error.message,
        consumersCount: 0,
        consumers: [],
      }
    }
  }
}

export default MqAdapter
export { CONSUMER_TYPES }
