import dotenv from 'dotenv'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env') })

// Project Configuration
export const PROJECT_CONFIG = {
  name: process.env.PROJECT_NAME || 'SevlteFurpa',
  version: process.env.PROJECT_VERSION || '1.0.0',
  description: process.env.PROJECT_DESCRIPTION || 'Electron.js desktop application',
}

// Database Configuration
export const DB_CONFIG = {
  name: process.env.DB_NAME || 'sevltefurpa.db',
  path: process.env.DB_PATH || './data',
}

// RabbitMQ Configuration
export const RABBITMQ_CONFIG = {
  host: process.env.RABBITMQ_HOST || 'localhost',
  port: parseInt(process.env.RABBITMQ_PORT) || 5672,
  username: process.env.RABBITMQ_USERNAME || 'guest',
  password: process.env.RABBITMQ_PASSWORD || 'guest',
  vhost: process.env.RABBITMQ_VHOST || '/',
  protocol: process.env.RABBITMQ_PROTOCOL || 'amqp',
  connectionTimeout: parseInt(process.env.RABBITMQ_CONNECTION_TIMEOUT) || 10000,
  heartbeat: parseInt(process.env.RABBITMQ_HEARTBEAT) || 60,

  // Connection URL
  get url() {
    return `${this.protocol}://${this.username}:${this.password}@${this.host}:${this.port}${this.vhost}`
  },

  // Queues
  queues: {
    users: process.env.RABBITMQ_QUEUE_USERS || 'users_queue',
    notifications: process.env.RABBITMQ_QUEUE_NOTIFICATIONS || 'notifications_queue',
    logs: process.env.RABBITMQ_QUEUE_LOGS || 'logs_queue',
  },

  // Exchanges
  exchanges: {
    main: process.env.RABBITMQ_EXCHANGE_MAIN || 'sevltefurpa_exchange',
    type: process.env.RABBITMQ_EXCHANGE_TYPE || 'topic',
  },
}

// Application Settings
export const APP_CONFIG = {
  window: {
    width: parseInt(process.env.APP_WINDOW_WIDTH) || 1377,
    height: parseInt(process.env.APP_WINDOW_HEIGHT) || 768,
    fullscreen: process.env.APP_FULLSCREEN === 'true',
    devTools: process.env.APP_DEV_TOOLS === 'true',
  },
  security: {
    nodeIntegration: process.env.SECURITY_NODE_INTEGRATION === 'true',
    contextIsolation: process.env.SECURITY_CONTEXT_ISOLATION === 'true',
    webSecurity: process.env.SECURITY_WEB_SECURITY === 'true',
    allowInsecureContent: process.env.SECURITY_ALLOW_INSECURE_CONTENT === 'true',
  },
  isDev: process.env.NODE_ENV === 'development',
}

// Logging Configuration
export const LOG_CONFIG = {
  level: process.env.LOG_LEVEL || 'info',
  file: process.env.LOG_FILE || './logs/app.log',
}

// Export all configurations
export default {
  PROJECT_CONFIG,
  DB_CONFIG,
  RABBITMQ_CONFIG,
  APP_CONFIG,
  LOG_CONFIG,
}
